package com.scuba156.carapp.core.media

import android.app.Application
import android.graphics.Bitmap
import android.media.session.MediaController
import android.media.session.PlaybackState
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.scuba156.carapp.core.media.spotify.RecommendationCategory
import com.scuba156.carapp.core.media.spotify.SpotifyPlayer
import com.spotify.protocol.types.Image
import com.spotify.protocol.types.ImageUri

class UniversalMediaPlayer(application: Application) : AndroidViewModel(application), IMediaPlayerNew {
    
    companion object {
        private const val TAG = "UniversalMediaPlayer"
    }
    
    // Spotify player for when Spotify is active
    private val spotifyPlayer = SpotifyPlayer(application)
    
    // Current active media controller (for non-Spotify apps)
    private var currentMediaController: MediaController? = null
    
    // Current media source type
    private var currentMediaSource: MediaSource = MediaSource.NONE
    
    enum class MediaSource {
        NONE,
        SPOTIFY,
        ANDROID_MEDIA_SESSION
    }
    
    // LiveData properties implementing IMediaPlayerNew
    override val currentRecommendations: MutableLiveData<MutableList<RecommendationCategory>> by lazy {
        MutableLiveData<MutableList<RecommendationCategory>>()
    }
    
    override val currentPlayerState: MutableLiveData<MediaMetaData> by lazy {
        MutableLiveData<MediaMetaData>()
    }
    
    override val currentConnectionState: MutableLiveData<ConnectionState> by lazy {
        MutableLiveData<ConnectionState>()
    }
    
    init {
        // Observe Spotify player state changes
        spotifyPlayer.currentPlayerState.observeForever { spotifyMetadata ->
            if (currentMediaSource == MediaSource.SPOTIFY) {
                currentPlayerState.value = spotifyMetadata
            }
        }
        
        // Observe Spotify connection state changes
        spotifyPlayer.currentConnectionState.observeForever { spotifyConnectionState ->
            if (currentMediaSource == MediaSource.SPOTIFY) {
                Log.d(TAG, "Spotify connection state changed to: $spotifyConnectionState")
                currentConnectionState.value = spotifyConnectionState
            }
        }
        
        // Observe Spotify recommendations
        spotifyPlayer.currentRecommendations.observeForever { recommendations ->
            if (currentMediaSource == MediaSource.SPOTIFY) {
                currentRecommendations.value = recommendations
            }
        }
        
        // Start with disconnected state
        currentConnectionState.value = ConnectionState.Disconnected
    }
    
    // Set the active media controller (called by MediaManager)
    fun setActiveMediaController(mediaController: MediaController?) {
        Log.d(TAG, "Setting active media controller: ${mediaController?.packageName}")
        
        // Unregister previous controller if exists
        currentMediaController?.unregisterCallback(mediaControllerCallback)
        
        currentMediaController = mediaController
        
        if (mediaController != null) {
            // Check if this is Spotify
            if (mediaController.packageName == "com.spotify.music") {
                switchToSpotifyMode()
            } else {
                switchToAndroidMediaMode(mediaController)
            }
        } else {
            switchToNoMediaMode()
        }
    }
    
    private fun switchToSpotifyMode() {
        Log.d(TAG, "Switching to Spotify mode")
        currentMediaSource = MediaSource.SPOTIFY

        // Update connection state immediately to current Spotify state
        val currentSpotifyState = spotifyPlayer.currentConnectionState.value ?: ConnectionState.Disconnected
        currentConnectionState.value = currentSpotifyState

        // Connect to Spotify if not already connected
        if (currentSpotifyState != ConnectionState.Connected) {
            Log.d(TAG, "Spotify not connected, attempting to connect...")
            spotifyPlayer.connect()
        } else {
            Log.d(TAG, "Spotify already connected")
        }
    }
    
    private fun switchToAndroidMediaMode(mediaController: MediaController) {
        Log.d(TAG, "Switching to Android Media mode for: ${mediaController.packageName}")
        currentMediaSource = MediaSource.ANDROID_MEDIA_SESSION
        
        // Register callback for media controller
        mediaController.registerCallback(mediaControllerCallback)
        
        // Update current state from media controller
        updateFromMediaController(mediaController)
        
        // Set connected state
        currentConnectionState.value = ConnectionState.Connected
        
        // Clear recommendations (only available for Spotify)
        currentRecommendations.value = mutableListOf()
    }
    
    private fun switchToNoMediaMode() {
        Log.d(TAG, "Switching to no media mode")
        currentMediaSource = MediaSource.NONE

        // If we have no media controller set, try Spotify as fallback
        Log.d(TAG, "No other media detected, falling back to Spotify")
        switchToSpotifyMode()
    }
    
    private fun updateFromMediaController(mediaController: MediaController) {
        val metadata = mediaController.metadata
        val playbackState = mediaController.playbackState
        if (metadata != null) {
            currentPlayerState.value = MediaMetaData(metadata, null, playbackState)
        }
    }
    
    // Media controller callback for non-Spotify apps
    private val mediaControllerCallback = object : MediaController.Callback() {
        override fun onMetadataChanged(metadata: android.media.MediaMetadata?) {
            super.onMetadataChanged(metadata)
            if (currentMediaSource == MediaSource.ANDROID_MEDIA_SESSION && metadata != null) {
                val playbackState = currentMediaController?.playbackState
                currentPlayerState.value = MediaMetaData(metadata, null, playbackState)
            }
        }

        override fun onPlaybackStateChanged(state: PlaybackState?) {
            super.onPlaybackStateChanged(state)
            Log.d(TAG, "Playback state changed: ${state?.state}")

            // Update the current player state with new playback info
            if (currentMediaSource == MediaSource.ANDROID_MEDIA_SESSION) {
                val metadata = currentMediaController?.metadata
                if (metadata != null) {
                    currentPlayerState.value = MediaMetaData(metadata, null, state)
                }
            }
        }
    }
    
    // IMediaPlayerNew interface implementation
    override fun togglePause() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.togglePause()
            MediaSource.ANDROID_MEDIA_SESSION -> {
                currentMediaController?.transportControls?.let { controls ->
                    val state = currentMediaController?.playbackState?.state
                    if (state == PlaybackState.STATE_PLAYING) {
                        controls.pause()
                    } else {
                        controls.play()
                    }
                }
            }
            MediaSource.NONE -> Log.w(TAG, "No active media source for togglePause")
        }
    }
    
    override fun nextSong() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.nextSong()
            MediaSource.ANDROID_MEDIA_SESSION -> currentMediaController?.transportControls?.skipToNext()
            MediaSource.NONE -> Log.w(TAG, "No active media source for nextSong")
        }
    }
    
    override fun previousSong() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.previousSong()
            MediaSource.ANDROID_MEDIA_SESSION -> currentMediaController?.transportControls?.skipToPrevious()
            MediaSource.NONE -> Log.w(TAG, "No active media source for previousSong")
        }
    }
    
    override fun connect() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.connect()
            MediaSource.ANDROID_MEDIA_SESSION -> {
                // Already connected via MediaManager
                currentConnectionState.value = ConnectionState.Connected
            }
            MediaSource.NONE -> {
                Log.d(TAG, "No active media source detected - checking for other media first")
                // Don't immediately default to Spotify - let MediaManager check for other active media first
                // This will be set by MediaManager if other media is found, otherwise we'll fallback to Spotify
                currentConnectionState.value = ConnectionState.Disconnected
            }
        }
    }
    
    override fun disconnect() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.disconnect()
            MediaSource.ANDROID_MEDIA_SESSION -> {
                currentMediaController?.unregisterCallback(mediaControllerCallback)
                currentConnectionState.value = ConnectionState.Disconnected
            }
            MediaSource.NONE -> Log.w(TAG, "No media source to disconnect from")
        }
    }
    
    override fun seekTo(position: Long) {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.seekTo(position)
            MediaSource.ANDROID_MEDIA_SESSION -> currentMediaController?.transportControls?.seekTo(position)
            MediaSource.NONE -> Log.w(TAG, "No active media source for seekTo")
        }
    }
    
    override fun play(uri: String) {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.play(uri)
            MediaSource.ANDROID_MEDIA_SESSION -> {
                // For non-Spotify apps, we can't play specific URIs
                Log.w(TAG, "Cannot play specific URI with Android Media Session")
            }
            MediaSource.NONE -> Log.w(TAG, "No active media source for play")
        }
    }
    
    override fun getImageCallback(imageUri: ImageUri, dimension: Image.Dimension, callback: (Bitmap) -> Unit) {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.getImageCallback(imageUri, dimension, callback)
            MediaSource.ANDROID_MEDIA_SESSION -> {
                // For non-Spotify apps, album art comes from MediaMetadata
                Log.w(TAG, "Album art for non-Spotify apps should come from MediaMetadata")
            }
            MediaSource.NONE -> Log.w(TAG, "No active media source for getImageCallback")
        }
    }
    
    override fun toggleShuffle() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.toggleShuffle()
            MediaSource.ANDROID_MEDIA_SESSION -> {
                // Not all media apps support shuffle via MediaController
                Log.w(TAG, "Shuffle not supported for all Android Media Sessions")
            }
            MediaSource.NONE -> Log.w(TAG, "No active media source for toggleShuffle")
        }
    }
    
    override fun toggleRepeat() {
        when (currentMediaSource) {
            MediaSource.SPOTIFY -> spotifyPlayer.toggleRepeat()
            MediaSource.ANDROID_MEDIA_SESSION -> {
                // Not all media apps support repeat via MediaController
                Log.w(TAG, "Repeat not supported for all Android Media Sessions")
            }
            MediaSource.NONE -> Log.w(TAG, "No active media source for toggleRepeat")
        }
    }
    
    // Getter for current media source (for debugging/UI purposes)
    fun getCurrentMediaSource(): MediaSource = currentMediaSource
    
    // Getter for current package name (for debugging/UI purposes)
    fun getCurrentPackageName(): String? = currentMediaController?.packageName
}
