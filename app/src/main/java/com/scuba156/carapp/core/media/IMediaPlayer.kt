package com.scuba156.carapp.core.media

import android.graphics.Bitmap
import androidx.lifecycle.MutableLiveData
import com.scuba156.carapp.core.media.spotify.RecommendationCategory
import com.scuba156.carapp.views.viewModels.ConnectionState
import com.spotify.protocol.types.Image
import com.spotify.protocol.types.ImageUri
import com.spotify.protocol.types.PlayerState

interface IMediaPlayer {

    abstract val currentRecommendations: MutableLiveData<MutableList<RecommendationCategory>>
    val currentPlayerState: MutableLiveData<PlayerState>

    val currentConnectionState: MutableLiveData<ConnectionState>

    fun togglePause()

    fun nextSong()

    fun previousSong()

    fun connect()

    fun disconnect()

    fun seekTo(position: Long)

    fun play(uri: String)

    abstract fun getImageCallback(imageUri: ImageUri, dimension: Image.Dimension, callback: (Bitmap) -> Unit)

    fun toggleShuffle()

    fun toggleRepeat()
}