package com.scuba156.carapp.core.media

import android.content.ComponentName
import android.content.Context
import android.media.session.MediaController
import android.media.session.MediaSessionManager
import android.media.session.PlaybackState
import android.provider.Settings
import android.util.Log
import com.scuba156.carapp.views.activities.MainActivity

object MediaManager: MediaSessionManager.OnActiveSessionsChangedListener{
    var TAG = "MediaManager"
    private lateinit var sessionManager : MediaSessionManager
    private lateinit var mainActivity: MainActivity
    private var currentMediaControllerCallback: MediaControllerCallback? = null

    private lateinit var universalMediaPlayer: UniversalMediaPlayer

    override fun onActiveSessionsChanged(controllers: MutableList<MediaController>?) {
        Log.d(TAG, "Active media sessions changed")
        if (controllers != null) {
            Log.d(TAG, "Found ${controllers.size} media controllers")
            checkForActiveMediaController(controllers)
        } else {
            Log.d(TAG, "No media controllers available")
            universalMediaPlayer.setActiveMediaController(null)
        }
    }

    fun start(context: Context, main: MainActivity, universalMediaPlayer: UniversalMediaPlayer) {
        mainActivity = main
        sessionManager = context.getSystemService(Context.MEDIA_SESSION_SERVICE) as MediaSessionManager
        this.universalMediaPlayer = universalMediaPlayer

        try {
            val currentSessions = sessionManager.getActiveSessions(
                ComponentName(
                    context,
                    NotificationListener::class.java
                ))

            checkForActiveMediaController(currentSessions)
            startListening(context)

            Log.d(TAG, "MediaManager started successfully")
        } catch (e: SecurityException) {
            Log.w(TAG, "Missing notification listener permission. Media session detection disabled.")
            Log.w(TAG, "To enable universal media support, grant notification access in Settings > Apps > CarApp > Notifications > Notification access")

            // Set disconnected state since we can't access media sessions
            universalMediaPlayer.setActiveMediaController(null)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start MediaManager: ${e.message}")
            universalMediaPlayer.setActiveMediaController(null)
        }
    }

    fun getCurrentMediaControllerCallback(): MediaControllerCallback {
        if (this.currentMediaControllerCallback == null) {
            this.currentMediaControllerCallback = MediaControllerCallback()
        }
        return this.currentMediaControllerCallback!!
    }

    private fun checkForActiveMediaController(currentSessions: MutableList<MediaController>) {
        // Clear previous controller
        universalMediaPlayer.setActiveMediaController(null)
        mainActivity.mediaController = null

        // Find the most suitable active media controller
        var bestController: MediaController? = null

        currentSessions.forEach { controller ->
            if (controller.metadata != null && mediaControllerIsActive(controller.playbackState)) {
                Log.d(TAG, "Found active media controller: ${controller.packageName}")

                // Prefer Spotify if available, otherwise use the first active controller
                if (controller.packageName == "com.spotify.music" || bestController == null) {
                    bestController = controller
                }
            }
        }

        // Set the best controller we found
        if (bestController != null) {
            Log.d(TAG, "Setting active media controller: ${bestController!!.packageName}")
            mainActivity.mediaController = bestController
            universalMediaPlayer.setActiveMediaController(bestController)
        } else {
            Log.d(TAG, "No active media controllers found")
        }
    }

    private fun mediaControllerIsActive(it: PlaybackState?) : Boolean {
        // 3 2 6 8 4 5 10 9 11
        return it != null && (it.state == PlaybackState.STATE_PLAYING ||
                it.state == PlaybackState.STATE_BUFFERING ||
                it.state == PlaybackState.STATE_CONNECTING ||
                it.state == PlaybackState.STATE_FAST_FORWARDING ||
                it.state == PlaybackState.STATE_PAUSED ||
                it.state == PlaybackState.STATE_REWINDING ||
                it.state == PlaybackState.STATE_SKIPPING_TO_NEXT ||
                it.state == PlaybackState.STATE_SKIPPING_TO_PREVIOUS ||
                it.state == PlaybackState.STATE_SKIPPING_TO_QUEUE_ITEM)
    }

    private fun startListening(context: Context) {
        try {
            val mediaListener = this

            sessionManager.addOnActiveSessionsChangedListener(
                mediaListener, ComponentName(
                    context.packageName,
                    NotificationListener::class.java.name
                )
            )

            Log.d(TAG, "Started listening for media session changes")
        } catch (e: SecurityException) {
            Log.w(TAG, "Cannot listen for media session changes - missing notification listener permission")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start listening for media sessions: ${e.message}")
        }
    }

    // Helper method to check if notification listener permission is granted
    fun isNotificationListenerEnabled(context: Context): Boolean {
        val enabledListeners = Settings.Secure.getString(
            context.contentResolver,
            "enabled_notification_listeners"
        )
        return enabledListeners?.contains(context.packageName) == true
    }

    // Helper method to open notification listener settings
    fun openNotificationListenerSettings(context: Context) {
        try {
            val intent = android.content.Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open notification listener settings: ${e.message}")
        }
    }
}