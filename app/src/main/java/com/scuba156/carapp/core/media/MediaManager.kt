package com.scuba156.carapp.core.media

import android.content.ComponentName
import android.content.Context
import android.media.session.MediaController
import android.media.session.MediaSessionManager
import android.media.session.PlaybackState
import android.util.Log
import com.scuba156.carapp.views.activities.MainActivity

object MediaManager: MediaSessionManager.OnActiveSessionsChangedListener{
    var TAG = "MediaManager"
    private lateinit var sessionManager : MediaSessionManager
    private lateinit var mainActivity: MainActivity
    private var currentMediaControllerCallback: MediaControllerCallback? = null

    private lateinit var universalMediaPlayer: UniversalMediaPlayer

    override fun onActiveSessionsChanged(controllers: MutableList<MediaController>?) {
        Log.d(TAG, "Active media sessions changed")
        if (controllers != null) {
            Log.d(TAG, "Found ${controllers.size} media controllers")
            checkForActiveMediaController(controllers)
        } else {
            Log.d(TAG, "No media controllers available")
            universalMediaPlayer.setActiveMediaController(null)
        }
    }

    fun start(context: Context, main: MainActivity, universalMediaPlayer: UniversalMediaPlayer) {
        mainActivity = main
        sessionManager = context.getSystemService(Context.MEDIA_SESSION_SERVICE) as MediaSessionManager
        this.universalMediaPlayer = universalMediaPlayer

        val currentSessions = sessionManager.getActiveSessions(
            ComponentName(
                context,
                NotificationListener::class.java
            ))

        checkForActiveMediaController(currentSessions)

        startListening(context)
    }

    fun getCurrentMediaControllerCallback(): MediaControllerCallback {
        if (this.currentMediaControllerCallback == null) {
            this.currentMediaControllerCallback = MediaControllerCallback()
        }
        return this.currentMediaControllerCallback!!
    }

    private fun checkForActiveMediaController(currentSessions: MutableList<MediaController>) {
        // Clear previous controller
        universalMediaPlayer.setActiveMediaController(null)
        mainActivity.mediaController = null

        // Find the most suitable active media controller
        var bestController: MediaController? = null

        currentSessions.forEach { controller ->
            if (controller.metadata != null && mediaControllerIsActive(controller.playbackState)) {
                Log.d(TAG, "Found active media controller: ${controller.packageName}")

                // Prefer Spotify if available, otherwise use the first active controller
                if (controller.packageName == "com.spotify.music" || bestController == null) {
                    bestController = controller
                }
            }
        }

        // Set the best controller we found
        if (bestController != null) {
            Log.d(TAG, "Setting active media controller: ${bestController!!.packageName}")
            mainActivity.mediaController = bestController
            universalMediaPlayer.setActiveMediaController(bestController)
        } else {
            Log.d(TAG, "No active media controllers found")
        }
    }

    private fun mediaControllerIsActive(it: PlaybackState?) : Boolean {
        // 3 2 6 8 4 5 10 9 11
        return it != null && (it.state == PlaybackState.STATE_PLAYING ||
                it.state == PlaybackState.STATE_BUFFERING ||
                it.state == PlaybackState.STATE_CONNECTING ||
                it.state == PlaybackState.STATE_FAST_FORWARDING ||
                it.state == PlaybackState.STATE_PAUSED ||
                it.state == PlaybackState.STATE_REWINDING ||
                it.state == PlaybackState.STATE_SKIPPING_TO_NEXT ||
                it.state == PlaybackState.STATE_SKIPPING_TO_PREVIOUS ||
                it.state == PlaybackState.STATE_SKIPPING_TO_QUEUE_ITEM)
    }

    private fun startListening(context: Context) {
        val mediaListener = this

        sessionManager.addOnActiveSessionsChangedListener(
            mediaListener, ComponentName(
                context.packageName,
                NotificationListener::class.java.name
            )
        )
    }
}