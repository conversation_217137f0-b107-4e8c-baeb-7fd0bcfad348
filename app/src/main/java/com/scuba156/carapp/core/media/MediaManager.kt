package com.scuba156.carapp.core.media

import android.content.ComponentName
import android.content.Context
import android.media.session.MediaController
import android.media.session.MediaSessionManager
import android.media.session.PlaybackState
import android.provider.Settings
import android.util.Log
import com.scuba156.carapp.views.activities.MainActivity

object MediaManager: MediaSessionManager.OnActiveSessionsChangedListener{
    var TAG = "MediaManager"
    private lateinit var sessionManager : MediaSessionManager
    private lateinit var mainActivity: MainActivity
    private var currentMediaControllerCallback: MediaControllerCallback? = null

    private lateinit var universalMediaPlayer: UniversalMediaPlayer

    // Store current active sessions for access by other components
    private var currentActiveSessions: List<MediaController> = emptyList()

    override fun onActiveSessionsChanged(controllers: MutableList<MediaController>?) {
        Log.d(TAG, "Active media sessions changed")
        if (controllers != null) {
            Log.d(TAG, "Found ${controllers.size} media controllers")
            checkForActiveMediaController(controllers)
        } else {
            Log.d(TAG, "No media controllers available")
            universalMediaPlayer.setActiveMediaController(null)
        }
    }

    fun start(context: Context, main: MainActivity, universalMediaPlayer: UniversalMediaPlayer) {
        mainActivity = main
        sessionManager = context.getSystemService(Context.MEDIA_SESSION_SERVICE) as MediaSessionManager
        this.universalMediaPlayer = universalMediaPlayer

        try {
            val currentSessions = sessionManager.getActiveSessions(
                ComponentName(
                    context,
                    NotificationListener::class.java
                ))

            Log.d(TAG, "Found ${currentSessions.size} active media sessions")
            checkForActiveMediaController(currentSessions)
            startListening(context)

            // If no active media sessions found, fallback to Spotify after a delay
            if (currentSessions.isEmpty() || !hasActiveMediaController(currentSessions)) {
                Log.d(TAG, "No active media sessions found, will fallback to Spotify")
                // Post a delayed fallback to Spotify
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    if (universalMediaPlayer.getCurrentMediaSource() == UniversalMediaPlayer.MediaSource.NONE) {
                        Log.d(TAG, "Falling back to Spotify after no other media detected")
                        universalMediaPlayer.setActiveMediaController(null) // This will trigger Spotify fallback
                    }
                }, 1000) // 1 second delay to allow other media to be detected
            }

            Log.d(TAG, "MediaManager started successfully with notification listener permission")
        } catch (e: SecurityException) {
            Log.w(TAG, "Missing notification listener permission. Media session detection disabled.")
            Log.w(TAG, "To enable universal media support, grant notification access in Settings > Apps > CarApp > Notifications > Notification access")
            Log.w(TAG, "App will default to Spotify-only mode")

            // Set disconnected state since we can't access media sessions
            universalMediaPlayer.setActiveMediaController(null)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start MediaManager: ${e.message}")
            universalMediaPlayer.setActiveMediaController(null)
        }
    }

    fun getCurrentMediaControllerCallback(): MediaControllerCallback {
        if (this.currentMediaControllerCallback == null) {
            this.currentMediaControllerCallback = MediaControllerCallback()
        }
        return this.currentMediaControllerCallback!!
    }

    private fun checkForActiveMediaController(currentSessions: MutableList<MediaController>) {
        Log.d(TAG, "=== CHECKING FOR ACTIVE MEDIA CONTROLLERS ===")
        Log.d(TAG, "Total sessions found: ${currentSessions.size}")

        // Store current sessions for access by other components
        this.currentActiveSessions = currentSessions.toList()

        // Clear previous controller
        universalMediaPlayer.setActiveMediaController(null)
        mainActivity.mediaController = null

        // Log details about each session
        currentSessions.forEachIndexed { index, controller ->
            Log.d(TAG, "Session $index:")
            Log.d(TAG, "  Package: ${controller.packageName}")
            Log.d(TAG, "  Has metadata: ${controller.metadata != null}")
            Log.d(TAG, "  Playback state: ${controller.playbackState?.state}")
            Log.d(TAG, "  Is active: ${mediaControllerIsActive(controller.playbackState)}")

            if (controller.metadata != null) {
                Log.d(TAG, "  Title: ${controller.metadata?.getString(android.media.MediaMetadata.METADATA_KEY_TITLE)}")
                Log.d(TAG, "  Artist: ${controller.metadata?.getString(android.media.MediaMetadata.METADATA_KEY_ARTIST)}")
            }
        }

        // Find the most suitable active media controller
        var bestController: MediaController? = null
        var playingController: MediaController? = null

        currentSessions.forEach { controller ->
            if (controller.metadata != null && mediaControllerIsActive(controller.playbackState)) {
                Log.d(TAG, "Found active media controller: ${controller.packageName}")

                // Check if this controller is currently playing (not just paused)
                if (controller.playbackState?.state == PlaybackState.STATE_PLAYING) {
                    Log.d(TAG, "  -> Currently PLAYING: ${controller.packageName}")
                    playingController = controller
                } else {
                    Log.d(TAG, "  -> Currently PAUSED: ${controller.packageName}")
                }

                // Set as best controller if we don't have one yet
                if (bestController == null) {
                    bestController = controller
                }
            }
        }

        // Prioritize currently playing media over paused media
        val finalController = playingController ?: bestController

        // Set the best controller we found
        if (finalController != null) {
            Log.d(TAG, "Setting active media controller: ${finalController.packageName}")
            if (playingController != null) {
                Log.d(TAG, "  -> Chose PLAYING app over paused apps")
            }
            mainActivity.mediaController = finalController
            universalMediaPlayer.setActiveMediaController(finalController)
        } else {
            Log.d(TAG, "No active media controllers found")
        }
        Log.d(TAG, "=== END MEDIA CONTROLLER CHECK ===")
    }

    private fun mediaControllerIsActive(it: PlaybackState?) : Boolean {
        // 3 2 6 8 4 5 10 9 11
        return it != null && (it.state == PlaybackState.STATE_PLAYING ||
                it.state == PlaybackState.STATE_BUFFERING ||
                it.state == PlaybackState.STATE_CONNECTING ||
                it.state == PlaybackState.STATE_FAST_FORWARDING ||
                it.state == PlaybackState.STATE_PAUSED ||
                it.state == PlaybackState.STATE_REWINDING ||
                it.state == PlaybackState.STATE_SKIPPING_TO_NEXT ||
                it.state == PlaybackState.STATE_SKIPPING_TO_PREVIOUS ||
                it.state == PlaybackState.STATE_SKIPPING_TO_QUEUE_ITEM)
    }

    private fun hasActiveMediaController(sessions: MutableList<MediaController>): Boolean {
        return sessions.any { controller ->
            controller.metadata != null && mediaControllerIsActive(controller.playbackState)
        }
    }

    private fun startListening(context: Context) {
        try {
            val mediaListener = this

            sessionManager.addOnActiveSessionsChangedListener(
                mediaListener, ComponentName(
                    context.packageName,
                    NotificationListener::class.java.name
                )
            )

            Log.d(TAG, "Started listening for media session changes")
        } catch (e: SecurityException) {
            Log.w(TAG, "Cannot listen for media session changes - missing notification listener permission")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start listening for media sessions: ${e.message}")
        }
    }

    // Helper method to check if notification listener permission is granted
    fun isNotificationListenerEnabled(context: Context): Boolean {
        val enabledListeners = Settings.Secure.getString(
            context.contentResolver,
            "enabled_notification_listeners"
        )

        Log.d(TAG, "Checking notification listener permission...")
        Log.d(TAG, "Package name: ${context.packageName}")
        Log.d(TAG, "Enabled listeners: $enabledListeners")

        val isEnabled = enabledListeners?.contains(context.packageName) == true
        Log.d(TAG, "Notification listener enabled: $isEnabled")

        return isEnabled
    }

    // Helper method to open notification listener settings
    fun openNotificationListenerSettings(context: Context) {
        try {
            val intent = android.content.Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open notification listener settings: ${e.message}")
        }
    }

    // Method to manually refresh media sessions (call after permission is granted)
    fun refreshMediaSessions(context: Context) {
        if (isNotificationListenerEnabled(context)) {
            try {
                val currentSessions = sessionManager.getActiveSessions(
                    ComponentName(
                        context,
                        NotificationListener::class.java
                    ))

                Log.d(TAG, "Refreshing media sessions - found ${currentSessions.size} active sessions")
                checkForActiveMediaController(currentSessions)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to refresh media sessions: ${e.message}")
            }
        } else {
            Log.w(TAG, "Cannot refresh media sessions - notification listener permission not granted")
        }
    }

    // Public method to get current active sessions for UI components
    fun getActiveSessions(): List<MediaController> {
        return currentActiveSessions
    }

    // Helper method to get active sessions with metadata for dropdown
    fun getActiveSessionsWithMetadata(): List<ActiveMediaSession> {
        return currentActiveSessions.mapNotNull { controller ->
            try {
                if (controller.metadata != null && mediaControllerIsActive(controller.playbackState)) {
                    val packageManager = mainActivity.packageManager
                    val appName = try {
                        val appInfo = packageManager.getApplicationInfo(controller.packageName, 0)
                        val appLabel = packageManager.getApplicationLabel(appInfo).toString()
                        Log.d(TAG, "Package: ${controller.packageName} -> App Name: $appLabel")
                        appLabel
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to get app name for ${controller.packageName}: ${e.message}")
                        // Fallback: try to make package name more readable
                        controller.packageName.split(".").lastOrNull()?.replaceFirstChar { it.uppercase() } ?: controller.packageName
                    }

                    val title = controller.metadata?.getString(android.media.MediaMetadata.METADATA_KEY_TITLE) ?: "Unknown"
                    val artist = controller.metadata?.getString(android.media.MediaMetadata.METADATA_KEY_ARTIST) ?: "Unknown"

                    ActiveMediaSession(
                        packageName = controller.packageName,
                        appName = appName,
                        title = title,
                        artist = artist,
                        isPlaying = controller.playbackState?.state == android.media.session.PlaybackState.STATE_PLAYING,
                        controller = controller
                    )
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error getting session info for ${controller.packageName}: ${e.message}")
                null
            }
        }
    }

    // Data class for active media session info
    data class ActiveMediaSession(
        val packageName: String,
        val appName: String,
        val title: String,
        val artist: String,
        val isPlaying: Boolean,
        val controller: MediaController
    )

    // Method to switch to a specific media controller
    fun switchToMediaController(controller: MediaController) {
        Log.d(TAG, "Switching to media controller: ${controller.packageName}")
        mainActivity.mediaController = controller
        universalMediaPlayer.setActiveMediaController(controller)
    }
}