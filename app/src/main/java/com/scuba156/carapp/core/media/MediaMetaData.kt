package com.scuba156.carapp.core.media

import android.graphics.Bitmap
import android.media.MediaMetadata
import android.util.Log
import com.spotify.protocol.types.PlayerState

data class MediaMetaData(
    private val androidMetaData: MediaMetadata?,
    private val spotifyMetadata: PlayerState?,
    private val playbackState: android.media.session.PlaybackState? = null
) {
    lateinit var ArtistName: String
    lateinit var SongTitle: String
    var AlbumName: String? = null
    var Duration: Long = 0
    var Position: Long = 0
    var AlbumArt: Bitmap? = null
    var IsPlaying: Boolean = false
    var IsSpotify: Boolean = false

    init {
        if (androidMetaData != null) {
            importAndroidMetaData(androidMetaData)
            importPlaybackState(playbackState)
        } else if (spotifyMetadata != null) {
            importSpotifyMetaData(spotifyMetadata)
            IsSpotify = true
        } else {
            Log.e("MediaMetaData", "Both spotify and android meta data were null")
        }
    }

    private fun importAndroidMetaData(androidMetaData: MediaMetadata) {
        ArtistName = androidMetaData.getString(MediaMetadata.METADATA_KEY_ARTIST) ?: "Unknown Artist"
        SongTitle = androidMetaData.getString(MediaMetadata.METADATA_KEY_TITLE) ?: "Unknown Song"
        AlbumName = androidMetaData.getString(MediaMetadata.METADATA_KEY_ALBUM)
        Duration = androidMetaData.getLong(MediaMetadata.METADATA_KEY_DURATION)

        // Get album art if available
        AlbumArt = androidMetaData.getBitmap(MediaMetadata.METADATA_KEY_ALBUM_ART)
            ?: androidMetaData.getBitmap(MediaMetadata.METADATA_KEY_ART)
    }

    private fun importSpotifyMetaData(spotifyMetadata: PlayerState) {
        ArtistName = spotifyMetadata.track.artist.name
        SongTitle = spotifyMetadata.track.name
        AlbumName = spotifyMetadata.track.album.name
        Duration = spotifyMetadata.track.duration
        Position = spotifyMetadata.playbackPosition
        IsPlaying = !spotifyMetadata.isPaused
    }

    private fun importPlaybackState(playbackState: android.media.session.PlaybackState?) {
        if (playbackState != null) {
            Position = playbackState.position
            IsPlaying = playbackState.state == android.media.session.PlaybackState.STATE_PLAYING
        }
    }
}
