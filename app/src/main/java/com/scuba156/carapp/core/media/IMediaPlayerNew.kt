package com.scuba156.carapp.core.media

import android.graphics.Bitmap
import androidx.lifecycle.MutableLiveData
import com.scuba156.carapp.core.media.spotify.RecommendationCategory
import com.spotify.protocol.types.Image
import com.spotify.protocol.types.ImageUri

enum class ConnectionState {
    Disconnected,
    Connected,
    Error
}

interface IMediaPlayerNew {

    abstract val currentRecommendations: MutableLiveData<MutableList<RecommendationCategory>>
    val currentPlayerState: MutableLiveData<MediaMetaData>

    val currentConnectionState: MutableLiveData<ConnectionState>

    fun togglePause()

    fun nextSong()

    fun previousSong()

    fun connect()

    fun disconnect()

    fun seekTo(position: Long)

    fun play(uri: String)

    abstract fun getImageCallback(imageUri: ImageUri, dimension: Image.Dimension, callback: (Bitmap) -> Unit)

    fun toggleShuffle()

    fun toggleRepeat()
}