package com.scuba156.carapp.views.activities

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.widget.ImageButton
import androidx.core.app.ActivityCompat
import androidx.core.view.WindowCompat
import androidx.lifecycle.ViewModelProvider
import androidx.preference.PreferenceManager
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDragHandleView
import com.scuba156.carapp.BT_AUTOSTART_NOTIFICATION_ID
import com.scuba156.carapp.core.media.MediaManager
import com.scuba156.carapp.views.viewModels.UniversalMediaViewModel
import com.scuba156.carapp.NotificationsManager
import com.scuba156.carapp.R
import com.scuba156.carapp.utils.AppUtils
import com.scuba156.carapp.utils.ThemeType
import com.scuba156.carapp.utils.ThemeUtils

class MainActivity : AppCompatActivity() {

    private var isFullscreen: Boolean = false
    private var currentTheme: ThemeType = ThemeType.Dark
    private var bottomSheetBehavior: BottomSheetBehavior<*>? = null

    private val hideHandler = Handler(Looper.myLooper()!!)

    private val hidePart2Runnable = Runnable {
        // Delayed removal of status and navigation bar
        findViewById<MaterialToolbar>(R.id.bottomToolbar)?.visibility = View.VISIBLE
        window.insetsController?.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
    }

    private val hideRunnable = Runnable { hide() }

    override fun onCreate(savedInstanceState: Bundle?) {
        setDefaultPreferenceValues()
        WindowCompat.setDecorFitsSystemWindows(window, false)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupSupportAppBarButtons()
        setupBottomAppBarMenu()
        setupBottomSheet()
        setupAutoStart()
        updateThemeToggleIcon()

//        setSupportActionBar(binding.toolbar)
        supportActionBar?.hide()

//        packageManager.setComponentEnabledSetting(
//            ComponentName(
//                this,
//                BTReceiver::class.java
//            ), PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP
//        )

//        if (Settings.Secure.getString(this.contentResolver, "enabled_notification_listeners")
//                .contains(
//                    applicationContext.packageName
//                )
//        ) {
//            //service is enabled do something
//        } else {
//            val intent = Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")
//            startActivity(intent)
//        }

//        MediaManager.start(applicationContext, this)

//        val mediaListener = MediaSessionListener()
//        val sessionManager = applicationContext.getSystemService(Context.MEDIA_SESSION_SERVICE) as MediaSessionManager
//
//        sessionManager.addOnActiveSessionsChangedListener(
//            mediaListener, ComponentName(
//                applicationContext.packageName,
//                MediaSessionListener::class.java.name
//            )
//        )

    }

//    override fun onCreateView(
//        parent: View?,
//        name: String,
//        context: Context,
//        attrs: AttributeSet
//    ): View? {
//        parent?.windowInsetsController?.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
//
//        return super.onCreateView(parent, name, context, attrs)
//    }

    override fun onStart() {
        super.onStart()
        hide()
        updateTheme()
    }

    override fun onStop() {
        super.onStop()
        show()
//        val uiModeManager = getSystemService(UI_MODE_SERVICE) as UiModeManager
//        uiModeManager.disableCarMode(UiModeManager.DISABLE_CAR_MODE_GO_HOME);
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)

        if (hasFocus) {
            hide()
        } else {
//            binding.bottomAppBarContainer.visibility = View.INVISIBLE
        }
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)

        delayedHide()
        // Ensure bottom sheet is set up after layout is complete
        setupBottomSheetIfNeeded()
    }

    private fun setupBottomSheetIfNeeded() {
        // Check if we need to reinitialize the bottom sheet (e.g., after orientation change)
        val bottomSheet = findViewById<View>(R.id.standard_bottom_sheet)
        if (bottomSheet != null && bottomSheetBehavior == null) {
            Log.d(TAG, "Reinitializing bottom sheet after orientation change")
            setupBottomSheet()
        }
    }

    private fun hide() {
        // Schedule a runnable to remove the status and navigation bar after a delay
        isFullscreen = false
        hideHandler.postDelayed(hidePart2Runnable, UI_ANIMATION_DELAY.toLong())
    }

    private fun show() {
        // Show the system bar
        window.insetsController?.show(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())

        isFullscreen = true
    }

    private fun delayedHide() {
        hideHandler.removeCallbacks(hideRunnable)
        hideHandler.postDelayed(hideRunnable, 100)
    }

    private fun updateTheme() {
        Log.e(TAG, "Updating theme")
        ThemeUtils.setTheme(this)
        currentTheme = ThemeUtils.currentThemeValue(applicationContext)
    }

    private fun setDefaultPreferenceValues() {
        PreferenceManager.setDefaultValues(this, R.xml.general_preferences, false)
        PreferenceManager.setDefaultValues(this, R.xml.location_preferences, false)
        PreferenceManager.setDefaultValues(this, R.xml.autostart_preferences, false)
    }

    private fun setupSupportAppBarButtons() {
        val assistButton = findViewById<ImageButton>(R.id.voiceAssistButton)
        assistButton?.setOnClickListener {
            if (AppUtils.isPackageInstalled(AppUtils.GOOGLE_ASSISTANT_PACKAGE_NAME, this)) {
                AppUtils.launchPackage(AppUtils.GOOGLE_ASSISTANT_PACKAGE_NAME, this)
            } else {
                Log.e(TAG, "Could not find google assistant")
            }
        }

        val dialerButton = findViewById<ImageButton>(R.id.dialerButton)
        dialerButton?.setOnClickListener {
//            Toast.makeText(this, "is null", Toast.LENGTH_SHORT).show()
//            AppUtils.launchPackage(Intent.ACTION_DIAL, this)

            var intent = Intent("android.intent.action.DIAL", null as Uri?)
            startActivity(intent)
        }

        val themeToggleButton = findViewById<ImageButton>(R.id.themeToggleButton)
        themeToggleButton?.setOnClickListener {
            this.currentTheme = ThemeUtils.toggleLightDarkTheme(this)
            updateThemeToggleIcon()
        }
    }

    private fun setupBottomAppBarMenu() {
        val bottomAppBar = findViewById<MaterialToolbar>(R.id.bottomToolbar)
        bottomAppBar?.setOnMenuItemClickListener {
            when (it.itemId) {
                R.id.settingsItem -> {
                    val intent = Intent(this, SettingsActivity::class.java)
//                    val bundle = ActivityOptions.makeSceneTransitionAnimation(this).toBundle()
                    startActivity(intent)
                    true
                }
                else -> false
            }
        }
    }

    private fun setupBottomSheet() {
        // Check if bottom sheet exists (only in portrait mode)
        val bottomSheet = findViewById<View>(R.id.standard_bottom_sheet)
        if (bottomSheet != null) {
            bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)

            // Convert dp to px for different heights
            val minHeightDp = 100  // Minified player
            val normalHeightDp = 260  // Normal player (default)
            val maxHeightDp = 500  // Player + recommendations

            val minHeightPx = (minHeightDp * resources.displayMetrics.density).toInt()
            val normalHeightPx = (normalHeightDp * resources.displayMetrics.density).toInt()

            // Configure bottom sheet behavior for 3 states
            bottomSheetBehavior?.apply {
                peekHeight = minHeightPx  // Collapsed state shows minified player
                halfExpandedRatio = normalHeightPx.toFloat() / resources.displayMetrics.heightPixels
                isHideable = false
                skipCollapsed = false
                isFitToContents = false  // Allow half-expanded state
                state = BottomSheetBehavior.STATE_HALF_EXPANDED // Start with normal size
            }

            // Set default state after layout is complete
            bottomSheet.post {
                bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HALF_EXPANDED
            }

            // Optional: Add callback to handle state changes
            bottomSheetBehavior?.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    val recommendationsCard = findViewById<View>(R.id.recommendationsCard)

                    when (newState) {
                        BottomSheetBehavior.STATE_EXPANDED -> {
                            Log.d(TAG, "Bottom sheet expanded - Player + Recommendations")
                            recommendationsCard?.visibility = View.VISIBLE
                        }
                        BottomSheetBehavior.STATE_HALF_EXPANDED -> {
                            Log.d(TAG, "Bottom sheet half-expanded - Normal Player")
                            recommendationsCard?.visibility = View.GONE
                        }
                        BottomSheetBehavior.STATE_COLLAPSED -> {
                            Log.d(TAG, "Bottom sheet collapsed - Minified Player")
                            recommendationsCard?.visibility = View.GONE
                        }
                        BottomSheetBehavior.STATE_DRAGGING -> {
                            Log.d(TAG, "Bottom sheet dragging")
                        }
                        BottomSheetBehavior.STATE_SETTLING -> {
                            Log.d(TAG, "Bottom sheet settling")
                        }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // Handle slide offset changes if needed
                }
            })
        } else {
            Log.d(TAG, "Bottom sheet not found - likely in landscape mode")
        }
    }

    private fun setupAutoStart() {
        val sharedPref = PreferenceManager.getDefaultSharedPreferences(this)

        if (sharedPref.getBoolean("enable_autostart", false)) {
            Log.e(TAG,"Trying to get permissions")
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.BLUETOOTH_CONNECT),
                5
            )
            NotificationsManager.cancelNotification(BT_AUTOSTART_NOTIFICATION_ID)
        }
    }

    private fun updateThemeToggleIcon() {
        var img = R.drawable.ic_dark_mode_icon
        if (currentTheme == ThemeType.Dark || ThemeUtils.currentDayNightMode(this) == ThemeType.Dark) {
            img = R.drawable.ic_light_mode_icon
        }

        findViewById<ImageButton>(R.id.themeToggleButton)?.setImageResource(img)
    }

    companion object {
        const val TAG = "MainActivity2"

        /**
         * Whether or not the system UI should be auto-hidden after
         * [AUTO_HIDE_DELAY_MILLIS] milliseconds.
         */
        private const val AUTO_HIDE = true

        /**
         * If [AUTO_HIDE] is set, the number of milliseconds to wait after
         * user interaction before hiding the system UI.
         */
        private const val AUTO_HIDE_DELAY_MILLIS = 3000

        /**
         * Some older devices needs a small delay between UI widget updates
         * and a change of the status and navigation bar.
         */
        private const val UI_ANIMATION_DELAY = 300
    }
}