package com.scuba156.carapp.views.viewModels

import android.app.Application
import android.location.Location
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnCameraMoveListener
import com.google.android.gms.maps.GoogleMap.OnCameraMoveStartedListener
import com.google.android.gms.maps.GoogleMap.OnMapClickListener
import com.google.android.gms.maps.GoogleMap.OnMyLocationButtonClickListener
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MapStyleOptions
import com.google.android.gms.maps.model.MarkerOptions
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.scuba156.carapp.core.location.LocationData
import com.scuba156.carapp.core.location.LocationProvider
import kotlinx.coroutines.launch


class MapsViewModel(application: Application) : AndroidViewModel(application),
    OnMapReadyCallback, OnCameraMoveListener, OnMapClickListener, OnMyLocationButtonClickListener, OnCameraMoveStartedListener {
//    private val context by lazy { getApplication<Application>().applicationContext }

    private lateinit var map: GoogleMap

    private var isMapInitStarted: Boolean = false
    private var isMapInitComplete: Boolean = false
    private var isFirstMapInit: Boolean = true
    private var shouldFollowCurrentLocation = true

    private var onMapReadyCallback: (() -> Unit)? = null

    private var lastLocation: LatLng? = null

    val currentSpeed: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }

    val needsMapInit: Boolean
        get() {
            return (!isMapInitStarted || !isMapInitComplete)
        }

    init {
        isMapInitComplete = false
        isMapInitStarted = false
    }

    fun registerOnMapReadyCallback(call: () -> Unit) {
        onMapReadyCallback = call
    }

    fun removeOnMapReadyCallback() {
        onMapReadyCallback = null
    }

    fun requestUpdates() {
        LocationProvider.registerCallback(TAG, ::updateLocation)
        LocationProvider.requestLocationUpdates()
    }

    fun stopRequestingUpdates() {
        LocationProvider.stopRequestingLocationUpdates()
    }

    fun setMapTheme(styleOptions: MapStyleOptions?) {
        if (!isMapInitComplete || !map.setMapStyle(styleOptions)) {
            Log.e(TAG, "Changing map style Error")
        }
    }

    override fun onMapReady(map: GoogleMap) {
        isMapInitComplete = true
        this.map = map
//        updateMapTheme()
        //this.map.setMapStyle(MapStyleOptions.loadRawResourceStyle(MainActivity.appContext, R.raw.maps_style_json))
        this.map.setOnCameraMoveStartedListener(this)
        this.map.setOnMyLocationButtonClickListener(this)
        this.map.setOnMapClickListener(this)
        this.map.setOnCameraMoveListener(this)

        updateLocationUI()

        // TODO: Comment this so it doesn't randomly crash
        onMapReadyCallback?.invoke()

        requestUpdates()
    }

    private fun updateCurrentSpeed(locationData: LocationData) {
//        if (useMetric)
        currentSpeed.postValue(locationData.speedInKMPerHour.toString())
//        else
//            currentSpeed.postValue(String.format(
//                "%d %s",
//                locationData.speedInMilesPerHour,
//                context.getString(R.string.miles_per_hour_suffix)
//            ))

//        currentSpeed.postValue(String.format(
//            "%d %s",
//            locationData.speedInKMPerHour,
//            context.getString(R.string.km_per_hour_suffix)
//        ))
    }

    override fun onCameraMoveStarted(reason: Int) {
        if (reason == OnCameraMoveStartedListener.REASON_GESTURE) {
            shouldFollowCurrentLocation = false
            map.uiSettings.isMyLocationButtonEnabled = true

            val cameraPosition = CameraPosition.Builder()
                .target(map.cameraPosition.target)
                .tilt(0f)
                .build()

            map.moveCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
        }
    }

    override fun onMyLocationButtonClick(): Boolean {
        shouldFollowCurrentLocation = true
//        map.uiSettings.isMyLocationButtonEnabled = false
        return false
    }

    override fun onMapClick(point: LatLng) {
        map.clear()

        // Creating MarkerOptions
        val options = MarkerOptions()
        options.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED))

        // Setting the position of the marker
        options.position(point)

        // Add new marker to the Google Map Android API V2
        map.addMarker(options)
    }

    private fun updateLocationUI() {
        try {
            map.uiSettings.isMapToolbarEnabled = true
            map.isMyLocationEnabled = true
            map.uiSettings.isMyLocationButtonEnabled = true
            map.uiSettings.isCompassEnabled = false
            map.isTrafficEnabled = true
            map.isBuildingsEnabled = false
        } catch (e: SecurityException) {
            Log.e("Exception: %s", e.message, e)
            Firebase.crashlytics.recordException(e)
        }
    }

    private fun LatLng.equalsDelta(other: LatLng, delta: Double = 0.00001): Boolean {
        val results = FloatArray(1)
        Location.distanceBetween(this.latitude, this.longitude, other.latitude, other.longitude, results)

        return results[0] < 1

//        if (abs(this.latitude / other.latitude - 1) < delta || abs(this.longitude / other.longitude - 1) < delta) {
//            Log.e(TAG, "Is equal")
//            Log.e(
//                TAG,
//                String.format(
//                    "Lat:%s Long:%s",
//                    this.latitude,
//                    this.longitude
//                )
//            )
//            Log.e(
//                TAG,
//                String.format(
//                    "Lat:%s Long:%s",
//                    other.latitude,
//                    other.longitude
//                )
//            )
//            return true
//        }
//        Log.e(TAG, "Not equal")
//        return false
    }

    private fun updateLocation(locationData: LocationData) {
        viewModelScope.launch {
            var currentLocation = LatLng(locationData.latitude, locationData.longitude)
            updateCurrentSpeed(locationData)
            if (isMapInitComplete) {
                if (shouldFollowCurrentLocation) {
                    if (lastLocation == null || !lastLocation!!.equalsDelta(currentLocation)) {// !lastLocationData!!.locationEqualsDelta(locationData)) {
                        val cameraPosition = CameraPosition.Builder()
                            .target(LatLng(locationData.latitude, locationData.longitude))
                            .bearing(locationData.bearing)
                            .tilt(DEFAULT_TILT)
                            .zoom(DEFAULT_ZOOM)
                            .build()
                        if (isFirstMapInit) {
                            map.moveCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
                            isFirstMapInit = false
                        } else {
                            map.animateCamera(
                                CameraUpdateFactory.newCameraPosition(cameraPosition),
                                800,
                                //LocationProvider.fastestUpdateIntervalInMilliseconds.toInt() + 1,
                                null
                            )
                        }
                    }
                    map.uiSettings.isMyLocationButtonEnabled = false
                }
            } else if (!isMapInitStarted) {
                isMapInitStarted = true
//            map.getMapAsync() {
//                onMapReady(it)
//                updateLocation(locationData)
//            }
            }
            lastLocation = currentLocation
        }
    }

//    private fun checkHasPermissions(): Boolean {
//        return ContextCompat.checkSelfPermission(
//            context, Manifest.permission.ACCESS_FINE_LOCATION
//        ) == PackageManager.PERMISSION_GRANTED
//    }

    companion object {
        private const val TAG = "Maps View Model"
        private const val DEFAULT_ZOOM = 17f
        private const val DEFAULT_TILT = 60f
    }

    override fun onCameraMove() {
        // Camera move events should not trigger additional camera moves
        // This was causing infinite recursion and crashes
        // If tilt adjustment is needed, it should be done in onCameraMoveStarted instead
    }
}