package com.scuba156.carapp.views.fragments.spotify

import android.annotation.SuppressLint
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.preference.PreferenceManager
import com.scuba156.carapp.R
import com.scuba156.carapp.core.listeners.OnSwipeTouchListener
import com.scuba156.carapp.core.media.ConnectionState
import com.scuba156.carapp.core.media.MediaMetaData
import com.scuba156.carapp.views.fragments.CustomFragment
import com.scuba156.carapp.core.media.spotify.TrackProgressBar
import com.scuba156.carapp.databinding.FragmentMediaPlayerBinding
import com.scuba156.carapp.utils.PrefUtils
import com.scuba156.carapp.views.viewModels.UniversalMediaViewModel
import com.spotify.protocol.types.Image
/**
 * A simple [Fragment] subclass.
 * Use the [MediaPlayerFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class MediaPlayerFragment : CustomFragment() {

    private enum class Direction {
        Left,
        Right
    }

    private lateinit var binding: FragmentMediaPlayerBinding

    private lateinit var universalMediaPlayer: UniversalMediaViewModel

    private lateinit var trackProgressBar: TrackProgressBar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        universalMediaPlayer = ViewModelProvider(requireActivity())[UniversalMediaViewModel::class.java]

        val playerStateObserver = Observer<MediaMetaData> { newPlayerState ->
            playerStateEventCallback(newPlayerState)
        }

        universalMediaPlayer.currentPlayerState.observe(this, playerStateObserver)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentMediaPlayerBinding.inflate(inflater, container, false)

        binding.playBtn.setOnClickListener { universalMediaPlayer.togglePause() }
        binding.nextBtn.setOnClickListener { universalMediaPlayer.nextSong() }
        binding.previousBtn.setOnClickListener { universalMediaPlayer.previousSong() }
        binding.shuffleBtn.setOnClickListener { universalMediaPlayer.toggleShuffle() }
        binding.repeatBtn.setOnClickListener { universalMediaPlayer.toggleRepeat() }

        binding.mediaMetaDataLayout.setOnTouchListener(object : OnSwipeTouchListener(requireContext()) {

            override fun onSwipeLeft() {
                super.onSwipeLeft()
                onMediaSwipe(Direction.Left)
            }

            override fun onSwipeRight() {
                super.onSwipeRight()
                onMediaSwipe(Direction.Right)
            }
        })

        trackProgressBar = TrackProgressBar(binding.seekBar)
        trackProgressBar.setOnProgressChangedListener {
            binding.startTime.text = millisecondsToMinutes(it)
        }
        trackProgressBar.setSeekStopListener {
            universalMediaPlayer.seekTo(it)
        }

        binding.seekBar.isEnabled = false

        return binding.root
    }

    private fun onMediaSwipe(swipeDirection: Direction) {
        val sharedPref: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(
            requireContext()
        )
        val invertSwipeDirection = sharedPref.getBoolean(PrefUtils.MEDIA_INVERT_SWIPE_DIRECTION, false)

        if ((swipeDirection == Direction.Right && ! invertSwipeDirection) || swipeDirection == Direction.Left && invertSwipeDirection) {
            universalMediaPlayer.previousSong()
        } else {
            universalMediaPlayer.nextSong()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        universalMediaPlayer.disconnect()
        Log.d(TAG, "Universal media player fragment destroyed")
    }
//
//    override fun onStart() {
//        super.onStart()
//
//        spotifyPlayer.connect()
//    }
//
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "Universal media player fragment resumed")

        if (universalMediaPlayer.currentConnectionState.value == ConnectionState.Connected) {
            universalMediaPlayer.currentPlayerState.value?.let {
                playerStateEventCallback(it)
            }
        } else {
            universalMediaPlayer.connect()
        }
    }

    override fun onStop() {
        super.onStop()
        universalMediaPlayer.disconnect()
    }

    private fun playerStateEventCallback(mediaMetaData: MediaMetaData) {
        updatePlayButton(mediaMetaData)

        updateShuffleButton(mediaMetaData)

        updateRepeatButton(mediaMetaData)

        updateCoverArt(mediaMetaData)

        updateTrackInfo(mediaMetaData)

        updateSeekbar(mediaMetaData)
    }

    private fun updatePlayButton(mediaMetaData: MediaMetaData) {
        // Use the actual play/pause state from MediaMetaData
        val img = if (mediaMetaData.IsPlaying) {
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_pause_icon)
        } else {
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_play_icon)
        }
        binding.playBtn.setImageDrawable(img)
    }

    private fun updateCoverArt(mediaMetaData: MediaMetaData) {
        when {
            // Priority 1: Use Android MediaMetadata album art (for universal apps)
            mediaMetaData.AlbumArt != null -> {
                binding.albumArt.setImageBitmap(mediaMetaData.AlbumArt)
            }
            // Priority 2: Use Spotify image URI (for Spotify)
            mediaMetaData.IsSpotify && mediaMetaData.SpotifyImageUri != null -> {
                universalMediaPlayer.universalMediaPlayer.getImageCallback(
                    mediaMetaData.SpotifyImageUri!!,
                    Image.Dimension.LARGE
                ) { bitmap ->
                    binding.albumArt.setImageBitmap(bitmap)
                }
            }
            // Priority 3: Default album art
            else -> {
                val defaultImg = ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_play_icon)
                binding.albumArt.setImageDrawable(defaultImg)
            }
        }
    }

    private fun updateTrackInfo(mediaMetaData: MediaMetaData) {
        binding.artistName.text = mediaMetaData.ArtistName
        binding.songTitle.text = mediaMetaData.SongTitle

        // Use actual duration and position if available
        if (mediaMetaData.Duration > 0) {
            binding.endTime.text = millisecondsToMinutes(mediaMetaData.Duration)
            binding.startTime.text = millisecondsToMinutes(mediaMetaData.Position)
        } else {
            binding.endTime.text = "--:--"
            binding.startTime.text = "0:00"
        }
    }

    private fun updateShuffleButton(mediaMetaData: MediaMetaData) {
        if (mediaMetaData.IsSpotify) {
            // Show shuffle button for Spotify with correct state
            binding.shuffleBtn.visibility = View.VISIBLE
            val img = if (mediaMetaData.HasShuffle) {
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_shuffle_on_icon)
            } else {
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_shuffle_off_icon)
            }
            binding.shuffleBtn.setImageDrawable(img)
        } else {
            // Hide shuffle button for non-Spotify sources
            binding.shuffleBtn.visibility = View.GONE
        }
    }

    private fun updateRepeatButton(mediaMetaData: MediaMetaData) {
        if (mediaMetaData.IsSpotify) {
            // Show repeat button for Spotify with correct state
            binding.repeatBtn.visibility = View.VISIBLE
            val img = if (mediaMetaData.HasRepeat) {
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_repeat_on_icon)
            } else {
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_repeat_off_icon)
            }
            binding.repeatBtn.setImageDrawable(img)
        } else {
            // Hide repeat button for non-Spotify sources
            binding.repeatBtn.visibility = View.GONE
        }
    }

    private fun millisecondsToMinutes(milliseconds: Long): String {
        val minutes = milliseconds / 1000 / 60
        val seconds = milliseconds / 1000 % 60

        return String.format("%d:%02d", minutes, seconds)
    }

    private fun updateSeekbar(mediaMetaData: MediaMetaData) {
        trackProgressBar.apply {
            if (mediaMetaData.IsPlaying) {
                unpause()
            } else {
                pause()
            }
        }

        if (mediaMetaData.Duration > 0) {
            // Enable seekbar if we have duration info (works for both Spotify and universal)
            binding.seekBar.max = mediaMetaData.Duration.toInt()
            binding.seekBar.isEnabled = true
            trackProgressBar.setDuration(mediaMetaData.Duration)
            trackProgressBar.update(mediaMetaData.Position)
        } else {
            // Disable seekbar if no duration info
            binding.seekBar.max = 100
            binding.seekBar.isEnabled = false
            binding.seekBar.progress = 0
        }
    }

    companion object {
        private const val TAG = "UniversalMediaPlayerFragment"

        @JvmStatic
        fun newInstance() =
            MediaPlayerFragment().apply {
                arguments = Bundle().apply {
                }
            }
    }
}