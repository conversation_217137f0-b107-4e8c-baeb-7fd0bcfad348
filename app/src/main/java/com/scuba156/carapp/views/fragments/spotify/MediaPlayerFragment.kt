package com.scuba156.carapp.views.fragments.spotify

import android.annotation.SuppressLint
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.preference.PreferenceManager
import com.scuba156.carapp.R
import com.scuba156.carapp.core.listeners.OnSwipeTouchListener
import com.scuba156.carapp.core.media.ConnectionState
import com.scuba156.carapp.core.media.MediaMetaData
import com.scuba156.carapp.views.fragments.CustomFragment
import com.scuba156.carapp.core.media.spotify.TrackProgressBar
import com.scuba156.carapp.databinding.FragmentMediaPlayerBinding
import com.scuba156.carapp.utils.PrefUtils
import com.scuba156.carapp.views.viewModels.UniversalMediaViewModel
import com.spotify.protocol.types.Image
/**
 * A simple [Fragment] subclass.
 * Use the [MediaPlayerFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class MediaPlayerFragment : CustomFragment() {

    private enum class Direction {
        Left,
        Right
    }

    private lateinit var binding: FragmentMediaPlayerBinding

    private lateinit var universalMediaPlayer: UniversalMediaViewModel

    private lateinit var trackProgressBar: TrackProgressBar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        universalMediaPlayer = ViewModelProvider(requireActivity())[UniversalMediaViewModel::class.java]

        val playerStateObserver = Observer<MediaMetaData> { newPlayerState ->
            playerStateEventCallback(newPlayerState)
        }

        universalMediaPlayer.currentPlayerState.observe(this, playerStateObserver)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentMediaPlayerBinding.inflate(inflater, container, false)

        binding.playBtn.setOnClickListener { universalMediaPlayer.togglePause() }
        binding.nextBtn.setOnClickListener { universalMediaPlayer.nextSong() }
        binding.previousBtn.setOnClickListener { universalMediaPlayer.previousSong() }
        binding.shuffleBtn.setOnClickListener { universalMediaPlayer.toggleShuffle() }
        binding.repeatBtn.setOnClickListener { universalMediaPlayer.toggleRepeat() }

        binding.mediaMetaDataLayout.setOnTouchListener(object : OnSwipeTouchListener(requireContext()) {

            override fun onSwipeLeft() {
                super.onSwipeLeft()
                onMediaSwipe(Direction.Left)
            }

            override fun onSwipeRight() {
                super.onSwipeRight()
                onMediaSwipe(Direction.Right)
            }
        })

        trackProgressBar = TrackProgressBar(binding.seekBar)
        trackProgressBar.setOnProgressChangedListener {
            binding.startTime.text = millisecondsToMinutes(it)
        }
        trackProgressBar.setSeekStopListener {
            universalMediaPlayer.seekTo(it)
        }

        binding.seekBar.isEnabled = false

        return binding.root
    }

    private fun onMediaSwipe(swipeDirection: Direction) {
        val sharedPref: SharedPreferences = PreferenceManager.getDefaultSharedPreferences(
            requireContext()
        )
        val invertSwipeDirection = sharedPref.getBoolean(PrefUtils.MEDIA_INVERT_SWIPE_DIRECTION, false)

        if ((swipeDirection == Direction.Right && ! invertSwipeDirection) || swipeDirection == Direction.Left && invertSwipeDirection) {
            universalMediaPlayer.previousSong()
        } else {
            universalMediaPlayer.nextSong()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        universalMediaPlayer.disconnect()
        Log.d(TAG, "Universal media player fragment destroyed")
    }
//
//    override fun onStart() {
//        super.onStart()
//
//        spotifyPlayer.connect()
//    }
//
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "Universal media player fragment resumed")

        if (universalMediaPlayer.currentConnectionState.value == ConnectionState.Connected) {
            universalMediaPlayer.currentPlayerState.value?.let {
                playerStateEventCallback(it)
            }
        } else {
            universalMediaPlayer.connect()
        }
    }

    override fun onStop() {
        super.onStop()
        universalMediaPlayer.disconnect()
    }

    private fun playerStateEventCallback(mediaMetaData: MediaMetaData) {
        updatePlayButton(mediaMetaData)

        updateShuffleButton(mediaMetaData)

        updateRepeatButton(mediaMetaData)

        updateCoverArt(mediaMetaData)

        updateTrackInfo(mediaMetaData)

        updateSeekbar(mediaMetaData)
    }

    private fun updatePlayButton(mediaMetaData: MediaMetaData) {
        // For universal media player, we'll use a default play button
        // The actual play/pause state would need to be tracked separately
        val img = ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_play_icon)
        binding.playBtn.setImageDrawable(img)
    }

    private fun updateCoverArt(mediaMetaData: MediaMetaData) {
        // For non-Spotify apps, we'll use a default album art
        // Album art from Android MediaMetadata would need to be handled differently
        val defaultImg = ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_play_icon)
        binding.albumArt.setImageDrawable(defaultImg)
    }

    private fun updateTrackInfo(mediaMetaData: MediaMetaData) {
        binding.artistName.text = mediaMetaData.ArtistName ?: "Unknown Artist"
        binding.songTitle.text = mediaMetaData.SongTitle ?: "Unknown Song"

        // For universal media, we don't have duration/position info easily available
        binding.endTime.text = "--:--"
        binding.startTime.text = "0:00"
    }

    private fun updateShuffleButton(mediaMetaData: MediaMetaData) {
        // For universal media, we'll show shuffle as off by default
        val img = ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_shuffle_off_icon)
        binding.shuffleBtn.setImageDrawable(img)
    }

    private fun updateRepeatButton(mediaMetaData: MediaMetaData) {
        // For universal media, we'll show repeat as off by default
        val img = ContextCompat.getDrawable(requireContext(), R.drawable.ic_media_repeat_off_icon)
        binding.repeatBtn.setImageDrawable(img)
    }

    private fun millisecondsToMinutes(milliseconds: Long): String {
        val minutes = milliseconds / 1000 / 60
        val seconds = milliseconds / 1000 % 60

        return String.format("%d:%02d", minutes, seconds)
    }

    private fun updateSeekbar(mediaMetaData: MediaMetaData) {
        // For universal media, we don't have detailed playback info
        // Disable the seekbar for now
        trackProgressBar.apply {
            pause() // Default to paused state
        }

        binding.seekBar.max = 100
        binding.seekBar.isEnabled = false // Disable seeking for universal media
        binding.seekBar.progress = 0
    }

    companion object {
        private const val TAG = "UniversalMediaPlayerFragment"

        @JvmStatic
        fun newInstance() =
            MediaPlayerFragment().apply {
                arguments = Bundle().apply {
                }
            }
    }
}