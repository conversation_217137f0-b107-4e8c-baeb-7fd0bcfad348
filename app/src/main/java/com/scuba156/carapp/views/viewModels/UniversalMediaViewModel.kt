package com.scuba156.carapp.views.viewModels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.scuba156.carapp.core.media.ConnectionState
import com.scuba156.carapp.core.media.MediaManager
import com.scuba156.carapp.core.media.MediaMetaData
import com.scuba156.carapp.core.media.UniversalMediaPlayer
import com.scuba156.carapp.core.media.spotify.RecommendationCategory

class UniversalMediaViewModel(application: Application) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "UniversalMediaViewModel"
    }
    
    // Universal media player instance
    val universalMediaPlayer = UniversalMediaPlayer(application)
    
    // Expose the media player's LiveData properties
    val currentPlayerState: MutableLiveData<MediaMetaData> = universalMediaPlayer.currentPlayerState
    val currentConnectionState: MutableLiveData<ConnectionState> = universalMediaPlayer.currentConnectionState
    val currentRecommendations: MutableLiveData<MutableList<RecommendationCategory>> = universalMediaPlayer.currentRecommendations
    
    init {
        // The MediaManager will be started from MainActivity
        // and will automatically connect to this universal media player
    }
    
    // Convenience methods that delegate to the universal media player
    fun togglePause() = universalMediaPlayer.togglePause()
    fun nextSong() = universalMediaPlayer.nextSong()
    fun previousSong() = universalMediaPlayer.previousSong()
    fun connect() = universalMediaPlayer.connect()
    fun disconnect() = universalMediaPlayer.disconnect()
    fun seekTo(position: Long) = universalMediaPlayer.seekTo(position)
    fun play(uri: String) = universalMediaPlayer.play(uri)
    fun toggleShuffle() = universalMediaPlayer.toggleShuffle()
    fun toggleRepeat() = universalMediaPlayer.toggleRepeat()
    
    // Get current media source info for debugging/UI
    fun getCurrentMediaSource() = universalMediaPlayer.getCurrentMediaSource()
    fun getCurrentPackageName() = universalMediaPlayer.getCurrentPackageName()
    
    override fun onCleared() {
        super.onCleared()
        universalMediaPlayer.disconnect()
    }
}
