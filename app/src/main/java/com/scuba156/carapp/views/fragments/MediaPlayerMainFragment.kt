package com.scuba156.carapp.views.fragments

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.scuba156.carapp.core.media.ConnectionState
import com.scuba156.carapp.databinding.FragmentMediaPlayerMainBinding
import com.scuba156.carapp.utils.AppUtils
import com.scuba156.carapp.views.viewModels.UniversalMediaViewModel

class MediaPlayerMainFragment : CustomFragment() {

    private lateinit var binding: FragmentMediaPlayerMainBinding

    private lateinit var universalMediaPlayer: UniversalMediaViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        universalMediaPlayer = ViewModelProvider(requireActivity())[UniversalMediaViewModel::class.java]

        val connectionStateObserver = Observer<ConnectionState> { newState ->
            onPlayerConnectionStateChange(newState)
        }

        universalMediaPlayer.currentConnectionState.observe(this, connectionStateObserver)
        Log.d(TAG, "Universal media player fragment created")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentMediaPlayerMainBinding.inflate(inflater, container, false)

        binding.installSpotifyButton.setOnClickListener { AppUtils.openOnPlayStore(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext()) }

        binding.openSpotifyButton.setOnClickListener {
                if (!tryLaunchSpotify())
                    AppUtils.openOnPlayStore(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext())
        }

        currentView = binding.loadingView
//        binding.playerViewPager?.adapter = SpotifyFragmentAdapter(this)

        return binding.root
    }

    private fun onPlayerConnectionStateChange(newState: ConnectionState) {
        when(newState) {
            ConnectionState.Connected -> {
                Log.d(TAG, "Universal media player connected")
                val currentSource = universalMediaPlayer.getCurrentMediaSource()
                val packageName = universalMediaPlayer.getCurrentPackageName()
                Log.d(TAG, "Media source: $currentSource, Package: $packageName")

                // Update recommendations only for Spotify
                if (packageName == "com.spotify.music") {
                    // For Spotify, we can show recommendations
                    // universalMediaPlayer.updateRecommendations() // This would need to be implemented
                }

                bringViewToFront(binding.playerView, true)
            }
            ConnectionState.Disconnected -> {
                Log.d(TAG, "Universal media player disconnected")
                bringViewToFront(binding.loadingView, true)
            }
            ConnectionState.Error -> {
                Log.d(TAG, "Universal media player error")
                bringViewToFront(binding.errorView, true)
            }
        }
    }

    private fun tryLaunchSpotify() : Boolean {
        if (AppUtils.isPackageInstalled(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext())) {
            AppUtils.launchPackage(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext(),Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            return true
        } else {
            return false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        universalMediaPlayer.disconnect()
        Log.d(TAG, "Fragment destroyed")
    }

    override fun onStart() {
        super.onStart()
        universalMediaPlayer.connect()
        Log.d(TAG, "Fragment started")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "Fragment resumed")
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "Fragment stopped - disconnecting")
        universalMediaPlayer.disconnect()
    }

    companion object {
        private const val TAG = "MediaPlayerMainFragment"
    }
}