package com.scuba156.carapp.views.fragments

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.scuba156.carapp.core.media.ConnectionState
import com.scuba156.carapp.core.media.MediaManager
import com.scuba156.carapp.databinding.FragmentMediaPlayerMainBinding
import com.scuba156.carapp.utils.AppUtils
import com.scuba156.carapp.views.viewModels.UniversalMediaViewModel

class MediaPlayerMainFragment : CustomFragment() {

    private lateinit var binding: FragmentMediaPlayerMainBinding

    private lateinit var universalMediaPlayer: UniversalMediaViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        universalMediaPlayer = ViewModelProvider(requireActivity())[UniversalMediaViewModel::class.java]

        val connectionStateObserver = Observer<ConnectionState> { newState ->
            onPlayerConnectionStateChange(newState)
        }

        universalMediaPlayer.currentConnectionState.observe(this, connectionStateObserver)
        Log.d(TAG, "Universal media player fragment created")
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentMediaPlayerMainBinding.inflate(inflater, container, false)

        binding.installSpotifyButton.setOnClickListener { AppUtils.openOnPlayStore(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext()) }

        binding.openSpotifyButton.setOnClickListener {
            if (!tryLaunchSpotify())
                AppUtils.openOnPlayStore(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext())
        }

        // Add click listener for enabling universal media support
        binding.installSpotifyButton.setOnClickListener {
            if (!MediaManager.isNotificationListenerEnabled(requireContext())) {
                // Show dialog explaining the permission and open settings
                showNotificationPermissionDialog()
            } else {
                // If permission is granted, open Play Store for Spotify
                AppUtils.openOnPlayStore(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext())
            }
        }

        currentView = binding.loadingView
//        binding.playerViewPager?.adapter = SpotifyFragmentAdapter(this)

        return binding.root
    }

    private fun onPlayerConnectionStateChange(newState: ConnectionState) {
        when(newState) {
            ConnectionState.Connected -> {
                Log.d(TAG, "Universal media player connected")
                val currentSource = universalMediaPlayer.getCurrentMediaSource()
                val packageName = universalMediaPlayer.getCurrentPackageName()
                Log.d(TAG, "Media source: $currentSource, Package: $packageName")

                // Update recommendations only for Spotify
                if (packageName == "com.spotify.music") {
                    // For Spotify, we can show recommendations
                    // universalMediaPlayer.updateRecommendations() // This would need to be implemented
                }

                bringViewToFront(binding.playerView, true)
            }
            ConnectionState.Disconnected -> {
                Log.d(TAG, "Universal media player disconnected")
                bringViewToFront(binding.loadingView, true)
            }
            ConnectionState.Error -> {
                Log.d(TAG, "Universal media player error")
                updateErrorViewText()
                bringViewToFront(binding.errorView, true)
            }
        }
    }

    private fun updateErrorViewText() {
        if (!MediaManager.isNotificationListenerEnabled(requireContext())) {
            // Update text for notification permission issue
            binding.textView2.text = "Limited Media Support"
            binding.textView3.text = "Currently only Spotify is supported. To enable YouTube Music, Apple Music, and other apps, grant notification access."
            binding.installSpotifyButton.text = "Enable Universal Support"
            binding.openSpotifyButton.text = "Open Spotify"
        } else {
            // Default Spotify error text
            binding.textView2.text = "Failed to connect to Spotify"
            binding.textView3.text = "You may need to open Spotify in the background first or install it from the Play Store"
            binding.installSpotifyButton.text = "Install Spotify"
            binding.openSpotifyButton.text = "Open Spotify"
        }
    }

    private fun tryLaunchSpotify() : Boolean {
        if (AppUtils.isPackageInstalled(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext())) {
            AppUtils.launchPackage(AppUtils.SPOTIFY_PACKAGE_NAME, requireContext(),Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            return true
        } else {
            return false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        universalMediaPlayer.disconnect()
        Log.d(TAG, "Fragment destroyed")
    }

    override fun onStart() {
        super.onStart()

        // Check if we have notification listener permission for universal media support
        if (!MediaManager.isNotificationListenerEnabled(requireContext())) {
            Log.d(TAG, "Notification listener permission not granted - limited to Spotify only")
            showNotificationPermissionGuidance()

            // Show permission dialog after a short delay (so UI is ready)
            view?.postDelayed({
                if (isAdded && !MediaManager.isNotificationListenerEnabled(requireContext())) {
                    showNotificationPermissionDialog()
                }
            }, 2000) // 2 second delay
        }

        universalMediaPlayer.connect()
        Log.d(TAG, "Fragment started")
    }

    private fun showNotificationPermissionGuidance() {
        Log.w(TAG, "=== UNIVERSAL MEDIA SUPPORT DISABLED ===")
        Log.w(TAG, "To enable support for YouTube Music, Apple Music, and other apps:")
        Log.w(TAG, "Notification listener permission is required")
        Log.w(TAG, "Currently only Spotify is supported.")
    }

    private fun showNotificationPermissionDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Enable Universal Media Support")
            .setMessage("To control YouTube Music, Apple Music, and other media apps, CarApp needs notification access.\n\n" +
                    "This allows the app to detect what music is playing from any app.\n\n" +
                    "Steps:\n" +
                    "1. Tap 'Open Settings' below\n" +
                    "2. Find 'CarApp' in the list\n" +
                    "3. Toggle the switch to enable\n" +
                    "4. Return to this app\n\n" +
                    "Note: This permission is only used to detect media playback, not to read your notifications.")
            .setPositiveButton("Open Settings") { _, _ ->
                openNotificationListenerSettings()
            }
            .setNegativeButton("Maybe Later") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("Use Spotify Only") { dialog, _ ->
                dialog.dismiss()
                // User chose to stick with Spotify only
            }
            .show()
    }

    private fun openNotificationListenerSettings() {
        try {
            // Try the direct notification listener settings first
            val intent = Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            Log.d(TAG, "Opened notification listener settings")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to open notification listener settings directly: ${e.message}")
            try {
                // Fallback to general app settings
                val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = android.net.Uri.fromParts("package", requireContext().packageName, null)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                Log.d(TAG, "Opened app settings as fallback")
            } catch (e2: Exception) {
                Log.e(TAG, "Failed to open any settings: ${e2.message}")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "Fragment resumed")

        // Check if notification listener permission was granted while away
        if (MediaManager.isNotificationListenerEnabled(requireContext())) {
            Log.d(TAG, "Notification listener permission detected - refreshing media sessions")
            MediaManager.refreshMediaSessions(requireContext())
        }
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "Fragment stopped - disconnecting")
        universalMediaPlayer.disconnect()
    }

    companion object {
        private const val TAG = "MediaPlayerMainFragment"
    }
}