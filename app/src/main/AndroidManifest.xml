<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- <uses-permission android:name="com.example.permission.MAPS_RECEIVE" /> -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" /> <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_CONNECT"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission-group.NEARBY_DEVICES"
        android:usesPermissionFlags="neverForLocation" />

    <queries>

        <!-- Explicit apps you know in advance about: -->
        <package android:name="com.google.android.apps.googleassistant" />
        <package android:name="com.spotify.music" />
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CarApp"
        tools:ignore="ObsoleteSdkInt"
        tools:targetApi="31">
        <activity
            android:name=".views.activities.StartupActivity"
            android:exported="true"
            android:theme="@style/Theme.CarApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".views.activities.WelcomeActivity"
            android:documentLaunchMode="intoExisting"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:exported="true"
            android:excludeFromRecents="false"
            android:theme="@style/Theme.CarApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>
<!--        <activity-->
<!--            android:name=".views.activities.MainActivity"-->
<!--            android:documentLaunchMode="intoExisting"-->
<!--            android:exported="true"-->
<!--            android:label="Car App 2">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="${crashlyticsCollectionEnabled}" />

        <activity
            android:name=".views.activities.SettingsActivity"
            android:exported="false"
            android:label="@string/title_activity_settings">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".views.activities.GetPermissionsActivity"
            android:exported="false"
            android:theme="@style/Theme.CarApp.Transparent">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDnNWNkGS4s31XaEF0X0J-0m0Z9InZW5t4" />
        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />

        <activity
            android:name=".views.activities.MainActivity"
            android:configChanges="keyboardHidden|screenSize"
            android:documentLaunchMode="intoExisting"
            android:enabled="true"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:theme="@style/Theme.CarApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>
        <service android:label="NotificationListener"
            android:name=".core.media.NotificationListener"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:exported="true"
            android:enabled="true">
           <intent-filter>
               <action android:name="android.service.notification.NotificationListenerService"/>
           </intent-filter>
        </service>
        <!-- <receiver android:name=".MediaReceiver" -->
        <!-- android:exported="true" -->
        <!-- android:enabled="true"> -->
        <!-- <intent-filter> -->
        <!-- &lt;!&ndash;                        <action android:name="com.spinninghead.music.metachanged"/>&ndash;&gt; -->
        <!-- <action android:name="com.jrtstudio.music.metachanged"/> -->
        <!-- <action android:name="com.jrtstudio.music.playstatechanged"/> -->
        <!-- <action android:name="com.jrtstudio.AnotherMusicPlayer.metachanged"/> -->
        <!-- <action android:name="com.jrtstudio.AnotherMusicPlayer.playstatechanged"/> -->
        <!-- <action android:name="com.android.music.metachanged"/> -->
        <!-- <action android:name="com.android.music.playstatechanged"/> -->
        <!-- <action android:name="com.htc.music.metachanged"/> -->
        <!-- <action android:name="com.htc.music.playstatechanged"/> -->
        <!-- <action android:name="com.rdio.android.metachanged"/> -->
        <!-- <action android:name="com.rdio.android.playstatechanged"/> -->
        <!-- <action android:name="fm.last.android.metachanged"/> -->
        <!-- <action android:name="com.miui.player.metachanged"/> -->
        <!-- <action android:name="com.miui.player.playstatechanged"/> -->
        <!-- <action android:name="com.real.IMP.metachanged"/> -->
        <!-- <action android:name="com.samsung.MusicPlayer.metachanged"/> -->
        <!-- <action android:name="com.samsung.sec.metachanged"/> -->
        <!-- <action android:name="com.samsung.music.metachanged"/> -->
        <!-- <action android:name="com.samsung.sec.android.MusicPlayer.metachanged"/> -->
        <!-- <action android:name="com.samsung.sec.android.MusicPlayer.playstatechanged"/> -->
        <!-- <action android:name="com.lge.music.metachanged"/> -->
        <!-- <action android:name="com.lge.music.playstatechanged"/> -->
        <!-- <action android:name="com.sec.android.app.music.metachanged"/> -->
        <!-- <action android:name="com.sec.android.app.music.playstatechanged"/> -->
        <!-- <action android:name="com.rhapsody.metachanged"/> -->
        <!-- <action android:name="com.rhapsody.playstatechanged"/> -->
        <!-- <action android:name="com.maxmpz.audioplayer.playstatechanged"/> -->
        <!-- <action android:name="net.jjc1138.android.scrobbler.action.MUSIC_STATUS"/> -->
        <!-- <action android:name="com.adam.aslfms.notify.playstatechanged"/> -->
        <!-- <action android:name="com.andrew.apollo.metachanged"/> -->
        <!-- <action android:name="com.amazon.mp3.playstatechanged"/> -->
        <!-- <action android:name="com.amazon.mp3.metachanged"/> -->
        <!-- <action android:name="com.spotify.music.metadatachanged"/> -->
        <!-- <action android:name="com.spotify.music.playbackstatechanged"/> -->
        <!-- <action android:name="com.nullsoft.winamp.metachanged"/> -->
        <!-- <action android:name="com.nullsoft.winamp.playstatechanged"/> -->
        <!-- <action android:name="com.jetappfactory.jetaudio.playstatechanged"/> -->
        <!-- <action android:name="com.jetappfactory.jetaudio.metachanged"/> -->
        <!-- <action android:name="com.jetappfactory.jetaudioplus.playstatechanged"/> -->
        <!-- <action android:name="com.jetappfactory.jetaudioplus.metachanged"/> -->
        <!-- <action android:name="com.e8tracks.playstatechanged"/> -->
        <!-- <action android:name="com.e8tracks.metachanged"/> -->
        <!-- <action android:name="com.doubleTwist.androidPlayer.metachanged"/> -->
        <!-- <action android:name="com.doubleTwist.androidPlayer.playstatechanged"/> -->
        <!-- <action android:name="com.tbig.playerpro.metachanged"/> -->
        <!-- <action android:name="com.tbig.playerpro.playstatechanged"/> -->
        <!-- <action android:name="com.tbig.playerprotrial.metachanged"/> -->
        <!-- <action android:name="com.tbig.playerprotrial.playstatechanged"/> -->
        <!-- <action android:name="com.spotify.music.playbackstatechanged" /> -->
        <!-- <action android:name="com.spotify.music.metadatachanged" /> -->
        <!-- <action android:name="com.samsung.sec.android.MusicPlayer.metachanged" /> -->
        <!-- <action android:name="com.samsung.sec.android.MusicPlayer.playbackcomplete" /> -->
        <!-- <action android:name="com.samsung.sec.android.MusicPlayer.playstatechanged" /> -->
        <!-- <action android:name="com.samsung.music.metachanged" /> -->
        <!-- <action android:name="com.samsung.music.playbackcomplete" /> -->
        <!-- <action android:name="com.samsung.music.playstatechanged" /> -->
        <!-- <action android:name="com.samsung.sec.metachanged" /> -->
        <!-- <action android:name="com.samsung.sec.playbackcomplete" /> -->
        <!-- <action android:name="com.samsung.sec.playstatechanged" /> -->
        <!-- <action android:name="com.samsung.sec.android.metachanged" /> -->
        <!-- <action android:name="com.samsung.sec.android.playbackcomplete" /> -->
        <!-- <action android:name="com.samsung.sec.android.playstatechanged" /> -->
        <!-- <action android:name="com.samsung.MusicPlayer.metachanged" /> -->
        <!-- <action android:name="com.samsung.MusicPlayer.playbackcomplete" /> -->
        <!-- <action android:name="com.samsung.MusicPlayer.playstatechanged" /> -->
        <!-- <action android:name="com.spotify.music.playbackstatechanged"/> -->
        <!-- <action android:name="com.spotify.music.metadatachanged"/> -->
        <!-- <action android:name="com.spotify.music.queuechanged"/> -->
        <!-- </intent-filter> -->
        <!-- </receiver> -->
        <receiver
            android:name=".core.receivers.BTReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
            </intent-filter>
        </receiver>
    </application>

</manifest>