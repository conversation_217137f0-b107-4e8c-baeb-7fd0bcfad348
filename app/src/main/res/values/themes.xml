<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.CarApp" parent="Theme.Material3.DayNight">
        <item  name="android:windowActivityTransitions">true</item>
<!--        <item name="windowNoTitle">true</item>-->
<!--        <item name="windowActionBar">false</item>-->
        <!-- Primary brand color. -->
        <item name="colorPrimary">#00A651</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">#00A651</item>
        <item name="colorOnPrimaryContainer">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/teal_700</item>
        <item name="colorOnSecondaryContainer">@color/white</item>
        <!-- Status bar color. -->
<!--        <item name="android:statusBarColor">@color/white</item>-->
        <item name="fabButtonBackgroundTint">@color/teal_200</item>
        <item name="spotifyPlayerTextColor">@color/black</item>
        <item name="backgroundWindowColor">@color/white</item>
        <item name="android:textColor">@color/black</item>
        <item name="mediaPlayerBackground">@color/white</item>
        <item name="mediaPlayerBackgroundTint">#AAFFFFFF</item>
<!--        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialCardView.Cut</item>-->
<!--        <item name="android:textColor">@color/black</item>-->
<!--        <item name="popupTheme">@style/Theme.CarApp.Preferences.Fullscreen_Light.AppBar</item>-->
        <!-- Customize your theme here. -->
<!--        <item name="dividerColor">@color/quantum_orange</item>-->
<!--        <item name="divider">@color/quantum_orange</item>-->
<!--        <item name="android:listDivider">@color/quantum_orange</item>-->
<!--        <item name="dividerColor">@color/quantum_purple</item>-->
    </style>

    <style name="Theme.CarApp.Dark" parent="Theme.CarApp">
<!--        <item name="android:actionBarStyle">@style/Widget.Theme.CarApp.ActionBar.Fullscreen</item>-->
<!--        <item name="android:windowActionBarOverlay">true</item>-->
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowBackground">@color/black</item>
<!--        <item name="colorOnPrimary">@color/white</item>-->
        <item name="colorOnSecondary">@color/black</item>
<!--        <item name="colorPrimary">#00A651</item>-->
<!--        <item name="colorAccent">@color/white</item>-->
<!--        <item name="popupMenuStyle">@style/Theme.CarApp.Popup.Dark</item>-->
    </style>

<!--    <style name="Theme.CarApp.Light" parent="Theme.CarApp">-->
<!--        <item name="windowNoTitle">true</item>-->
<!--        <item name="windowActionBar">false</item>-->
<!--        <item name="colorOnPrimary">@color/black</item>-->
<!--        <item name="colorOnSecondary">@color/white</item>-->
<!--        <item name="android:windowBackground">@color/white</item>-->
<!--        <item name="colorPrimary">#00A651</item>-->
<!--        <item name="colorAccent">@color/black</item>-->
<!--        <item name="popupMenuStyle">@style/Theme.CarApp.Popup.Light</item>-->
<!--    </style>-->

<!--    <style name="Theme.CarApp.Preferences.Dark" parent="Theme.CarApp.Dark">-->
<!--        <item name="android:textColorPrimary">@color/white</item>-->
<!--        <item name="android:actionBarStyle">@style/Theme.CarApp.Preferences.ActionBar.Dark</item>-->
<!--        <item name="android:itemBackground">@color/black</item>-->
<!--&lt;!&ndash;        <item name="android:listDivider">@color/quantum_orange</item>&ndash;&gt;-->

<!--        &lt;!&ndash;        <item name="android:navigationBarDividerColor">@color/quantum_orange</item>&ndash;&gt;-->
<!--    </style>-->

<!--    <style name="CustomWidget.MaterialComponents.CompoundButton.Switch" parent="Widget.MaterialComponents.CompoundButton.Switch">-->
<!--        <item name="materialThemeOverlay">@style/Theme.CarApp.Switch.Light</item>-->
<!--    </style>-->

<!--    <style name="Theme.CarApp.Preferences.Light" parent="Theme.CarApp.Dark">-->
<!--        <item name="android:windowBackground">@color/white</item>-->
<!--        <item name="android:textColorPrimary">@color/black</item>-->
<!--        <item name="android:textColorSecondary">@color/quantum_grey500</item>-->
<!--        <item name="switchStyle">@style/Theme.CarApp.Preferences.Switch.Light</item>-->
<!--        <item name="android:actionBarStyle">@style/Theme.CarApp.Preferences.ActionBar.Light</item>-->
<!--        <item name="alertDialogTheme">@style/Theme.CarApp.Preferences.Dialog.Light</item>-->
<!--        <item name="checkboxStyle">@style/Theme.CarApp.Preferences.Checkbox.Light</item>-->
<!--        <item name="android:listDivider">@color/quantum_grey200</item>-->
<!--        <item name="android:dividerHeight">1dp</item>-->
<!--        <item name="allowDividerAfterLastItem">false</item>-->

<!--&lt;!&ndash;        <item name="preferenceFragmentCompatStyle">@style/Test</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="preferenceCategoryTitleTextAppearance">@style/Theme.CarApp.Preferences.Category.Light</item>&ndash;&gt;-->

<!--        <item name="preferenceCategoryTitleTextColor">@color/black</item>-->
<!--&lt;!&ndash;        <item name="android:childDivider">@color/light_blue_A200</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:colorAccent">@color/quantum_orange500</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:preferenceScreenStyle">@style/ListSeparatorText</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:divider">@color/black</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="layout_sty">@style/ListSeparatorText</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:textColorHint">@color/light_blue_A200</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:listDivider">@color/quantum_orange</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:listSeparatorTextViewStyle">@style/ListSeparatorText</item>&ndash;&gt;-->
<!--&lt;!&ndash;        <item name="android:divider">@null</item>&ndash;&gt;-->
<!--    </style>-->



    <style name="ThemeOverlay.CarApp.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/light_blue_600</item>
        <item name="fullscreenTextColor">@color/light_blue_A200</item>
<!--        <item name="colorPrimary">#00A651</item>-->
    </style>

</resources>