<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".views.fragments.spotify.MediaPlayerFragment">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:foreground="@color/semi_transparent_color">

        <ImageView
            android:id="@+id/albumArt"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="Album Art"
            android:scaleType="centerCrop"
            app:srcCompat="@android:drawable/screen_background_light" />

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/mediaMetaDataLayout"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:layout_toStartOf="@id/sourceImage"
            android:orientation="vertical">

            <TextView
                android:id="@+id/songTitle"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:ellipsize="marquee"
                android:fontFamily="sans-serif-condensed-medium"
                android:inputType="none"
                android:lines="2"
                android:maxLines="2"
                android:text="Song Title"
                android:textAlignment="viewStart"
                android:textColor="?attr/spotifyPlayerTextColor"
                android:textSize="20sp"
                android:textStyle="bold"
                android:typeface="normal" />

            <TextView
                android:id="@+id/artistName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:ems="10"
                android:fontFamily="sans-serif-condensed"
                android:inputType="none"
                android:lines="2"
                android:maxLines="2"
                android:text="Artist Name"
                android:textAlignment="viewStart"
                android:textColor="?attr/spotifyPlayerTextColor"
                android:textSize="18sp"
                android:typeface="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/sourceSelector"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="5dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:padding="4dp">

            <ImageView
                android:id="@+id/sourceImage"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/spotify_icon" />

            <ImageView
                android:id="@+id/dropdownArrow"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginStart="2dp"
                android:src="@drawable/ic_arrow_drop_down"
                android:tint="?attr/colorOnPrimary" />

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/mediaTrackbarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/mediaMetaDataLayout"
        android:layout_marginTop="10dp"
        android:orientation="vertical">

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:progress="50"
            android:progressBackgroundTint="#00A651"
            android:progressTint="#00A651"
            android:scaleY="1.5"
            android:thumb="@null"
            android:thumbTint="#00A651" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <TextView
                android:id="@+id/startTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="sans-serif"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="@string/song_start_time"
                android:textColor="?attr/colorOnPrimary" />

            <TextView
                android:id="@+id/endTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="sans-serif"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="13:30"
                android:textAlignment="textEnd"
                android:textColor="?attr/colorOnPrimary" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/mediaTrackbarLayout"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/mediaButtonsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/previousBtn"
                android:layout_width="80dp"
                android:layout_height="50dp"
                android:layout_margin="2dp"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_media_previous_icon"
                app:backgroundTint="@null"
                app:iconGravity="textStart"
                app:iconPadding="1dp"
                app:tint="@color/colorPrimary" />

            <ImageButton
                android:id="@+id/playBtn"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_margin="2dp"
                android:background="@drawable/round_outline"
                android:gravity="center"
                android:hapticFeedbackEnabled="true"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_media_play_icon"
                app:backgroundTint="@null"
                app:tint="@color/colorPrimary" />

            <ImageButton
                android:id="@+id/nextBtn"
                android:layout_width="80dp"
                android:layout_height="50dp"
                android:layout_margin="2dp"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center"
                android:hapticFeedbackEnabled="true"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_media_next_icon"
                app:backgroundTint="@null"
                app:iconGravity="textStart"
                app:iconPadding="1dp"
                app:tint="@color/colorPrimary" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="50dp"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/shuffleBtn"
                android:layout_width="50dp"
                android:layout_height="30dp"
                android:layout_margin="2dp"
                android:layout_weight="0.25"
                android:background="@null"
                android:gravity="center"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_media_shuffle_off_icon"
                app:backgroundTint="@null"
                app:iconGravity="textStart"
                app:iconPadding="1dp"
                app:tint="@color/colorPrimary" />

            <ImageButton
                android:id="@+id/repeatBtn"
                android:layout_width="50dp"
                android:layout_height="30dp"
                android:layout_margin="2dp"
                android:layout_weight="0.25"
                android:background="@null"
                android:gravity="center"
                android:hapticFeedbackEnabled="true"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_media_repeat_off_icon"
                app:backgroundTint="@null"
                app:iconGravity="textStart"
                app:iconPadding="1dp"
                app:tint="@color/colorPrimary" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>