<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:hapticFeedbackEnabled="true"
    tools:context=".views.fragments.MediaPlayerMainFragment">

    <LinearLayout
        android:id="@+id/loadingView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        tools:visibility="gone">

        <ProgressBar
            android:id="@+id/progressBar"
            style="@android:style/Widget.DeviceDefault.Light.ProgressBar.Large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="invisible"
        android:weightSum="4"
        tools:visibility="gone">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.5"
            android:gravity="center"
            android:text="Failed to connect to Spotify"
            android:textAlignment="gravity"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/textView3"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.9"
            android:text="You may need to open Spotify in the background first or install it from the Play Store"
            android:textAlignment="center"
            android:textSize="16sp"
            android:textStyle="bold" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/openSpotifyButton"
            android:layout_width="200dp"
            android:layout_height="0dp"
            android:layout_margin="2dp"
            android:layout_weight="0.8"
            android:background="@drawable/round_solid"
            android:gravity="center"
            android:text="Open Spotify"
            app:backgroundTint="@null" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/installSpotifyButton"
            android:layout_width="200dp"
            android:layout_height="0dp"
            android:layout_margin="2dp"
            android:layout_weight="0.8"
            android:background="@drawable/round_solid"
            android:gravity="center"
            android:text="Install Spotify"
            app:backgroundTint="@null" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="invisible"
        tools:visibility="visible">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragmentContainerView4"
            android:name="com.scuba156.carapp.views.fragments.spotify.MediaPlayerFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:layout="@layout/fragment_media_player" />

    </LinearLayout>

</FrameLayout>