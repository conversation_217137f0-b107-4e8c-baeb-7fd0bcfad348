<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/black_overlay"
    android:keepScreenOn="true"
    tools:context=".views.activities.MainActivity">

    <androidx.cardview.widget.CardView
        android:id="@+id/mapCard"
        android:layout_width="match_parent"
        android:layout_height="520dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragmentContainerView"
                android:name="com.scuba156.carapp.views.fragments.MapsFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.12"
                tools:layout="@layout/fragment_maps" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragmentContainerView3"
                android:name="com.scuba156.carapp.views.fragments.MapLocationFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.88"
                tools:layout="@layout/fragment_map_location" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>

<!--    <androidx.cardview.widget.CardView-->
<!--        android:id="@+id/forYouCard"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="240dp"-->
<!--        android:layout_below="@id/mapCard"-->
<!--        android:layout_alignStart="@id/mapCard"-->
<!--        android:layout_alignParentStart="true"-->
<!--        android:layout_marginStart="5dp"-->
<!--        android:layout_marginTop="5dp"-->
<!--        android:layout_marginEnd="5dp"-->
<!--        android:layout_marginBottom="5dp"-->
<!--        app:cardCornerRadius="10dp"-->
<!--        tools:visibility="gone" />-->

    <!-- Bottom Sheet for Media Player (Google Maps Style) -->
    <LinearLayout
        android:id="@+id/standard_bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_background"
        android:elevation="8dp"
        android:orientation="vertical"
        app:behavior_hideable="false"
        app:behavior_peekHeight="80dp"
        app:behavior_skipCollapsed="false"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <!-- Google Maps Style Drag Handle Container -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="12dp"
            android:paddingBottom="8dp"
            android:gravity="center_horizontal"
            android:clickable="true"
            android:focusable="true">

            <!-- Visible Drag Handle -->
            <View
                android:id="@+id/bottom_sheet_handle"
                android:layout_width="32dp"
                android:layout_height="4dp"
                android:background="@drawable/bottom_sheet_handle_maps_style"
                android:alpha="0.6" />
        </LinearLayout>

        <!-- Media Player Content -->
        <androidx.cardview.widget.CardView
            android:id="@+id/mediaPlayerCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:layout_marginTop="50dp"
            app:cardCornerRadius="0dp"
            app:cardElevation="0dp">

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragmentContainerView2"
                android:name="com.scuba156.carapp.views.fragments.MediaPlayerMainFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                tools:layout="@layout/fragment_media_player_main"
                tools:visibility="visible" />
        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Bottom App Bar Container -->
    <FrameLayout
        android:id="@+id/bottomAppBarContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginTop="5dp"
        android:translationZ="10dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/bottomToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#00FFFFFF"
            android:backgroundTint="#00FFFFFF"
            android:contentInsetEndWithActions="0dp"
            android:minHeight="?attr/actionBarSize"
            android:theme="?attr/actionBarTheme"
            app:contentInsetEnd="0dp"
            app:contentInsetEndWithActions="0dp"
            app:contentInsetRight="0dp"
            app:contentInsetStart="40dp"
            app:contentInsetStartWithNavigation="0dp"
            app:menu="@menu/bottom_app_bar_menu">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/voiceAssistButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.16"
                    android:background="@null"
                    android:src="@drawable/ic_toolbar_mic_icon" />

                <ImageButton
                    android:id="@+id/dialerButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.33"
                    android:background="@null"
                    android:src="@drawable/ic_toolbar_phone_icon" />

                <ImageButton
                    android:id="@+id/themeToggleButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.16"
                    android:background="@null"
                    android:src="@drawable/ic_dark_mode_icon" />

            </LinearLayout>

        </com.google.android.material.appbar.MaterialToolbar>

    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>