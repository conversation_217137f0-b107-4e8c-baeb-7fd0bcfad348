<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/black_overlay"
    android:keepScreenOn="true"
    tools:context=".views.activities.MainActivity">

    <androidx.cardview.widget.CardView
        android:id="@+id/mapCard"
        android:layout_width="match_parent"
        android:layout_height="520dp"
        android:layout_alignParentTop="true"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragmentContainerView"
                android:name="com.scuba156.carapp.views.fragments.MapsFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.12"
                tools:layout="@layout/fragment_maps" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragmentContainerView3"
                android:name="com.scuba156.carapp.views.fragments.MapLocationFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.88"
                tools:layout="@layout/fragment_map_location" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>

<!--    <androidx.cardview.widget.CardView-->
<!--        android:id="@+id/forYouCard"-->
<!--        android:layout_width="40dp"-->
<!--        android:layout_height="240dp"-->
<!--        android:layout_below="@id/mapCard"-->
<!--        android:layout_alignStart="@id/mapCard"-->
<!--        android:layout_alignParentStart="true"-->
<!--        android:layout_marginStart="5dp"-->
<!--        android:layout_marginTop="5dp"-->
<!--        android:layout_marginEnd="5dp"-->
<!--        android:layout_marginBottom="5dp"-->
<!--        app:cardCornerRadius="10dp"-->
<!--        tools:visibility="gone" />-->

    <FrameLayout
    android:id="@+id/standard_bottom_sheet"
        android:layout_width="250dp"
        android:layout_height="280dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
        android:layout_above="@+id/bottomAppBarContainer"
        android:layout_below="@id/mapCard"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp">


    <androidx.cardview.widget.CardView
        android:id="@+id/mediaPlayerCard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        app:cardCornerRadius="20dp">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragmentContainerView2"
            android:name="com.scuba156.carapp.views.fragments.MediaPlayerMainFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            tools:layout="@layout/fragment_media_player_main"
            tools:visibility="visible" />
    </androidx.cardview.widget.CardView>

    </FrameLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/bottomAppBarContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:layout_marginTop="5dp"
        android:translationZ="5dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/bottomToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#00FFFFFF"
            android:backgroundTint="#00FFFFFF"
            android:contentInsetEndWithActions="0dp"
            android:minHeight="?attr/actionBarSize"
            android:theme="?attr/actionBarTheme"
            app:contentInsetEnd="0dp"
            app:contentInsetEndWithActions="0dp"
            app:contentInsetRight="0dp"
            app:contentInsetStart="40dp"
            app:contentInsetStartWithNavigation="0dp"
            app:menu="@menu/bottom_app_bar_menu">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/voiceAssistButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.16"
                    android:background="@null"
                    android:src="@drawable/ic_toolbar_mic_icon" />

                <ImageButton
                    android:id="@+id/dialerButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.33"
                    android:background="@null"
                    android:src="@drawable/ic_toolbar_phone_icon" />

                <ImageButton
                    android:id="@+id/themeToggleButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.16"
                    android:background="@null"
                    android:src="@drawable/ic_dark_mode_icon" />

            </LinearLayout>

        </com.google.android.material.appbar.MaterialToolbar>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>