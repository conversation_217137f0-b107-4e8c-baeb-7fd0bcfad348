<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.CarApp" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">#00A651</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorPrimaryContainer">#00A651</item>
        <item name="colorOnPrimaryContainer">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorSecondaryContainer">@color/teal_700</item>
        <item name="colorOnSecondaryContainer">@color/white</item>
        <!-- Surface colors for dark theme -->
        <item name="colorSurface">@color/black</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnSurfaceVariant">@color/white</item>
        <item name="fabButtonBackgroundTint">@color/teal_200</item>
        <!-- Status bar color. -->
<!--        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>-->
        <item name="spotifyPlayerTextColor">@color/white</item>
        <item name="backgroundWindowColor">@color/black_overlay</item>
        <item name="android:textColor">@color/white</item>
        <item name="mediaPlayerBackground">@color/white</item>
        <item name="mediaPlayerBackgroundTint">@color/black_overlay</item>
        <item name="android:windowBackground">@color/black</item>
<!--        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialCardView.Cut</item>-->

        <!-- Customize your theme here. -->
    </style>

    <style name="ThemeOverlay.CarApp.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/light_blue_900</item>
        <item name="fullscreenTextColor">@color/light_blue_A400</item>
    </style>
</resources>